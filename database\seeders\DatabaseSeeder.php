<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Database\Seeders\RolePermissionSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // Ajout du seeder des rôles et permissions
        $this->call(RolePermissionSeeder::class);

        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
        $user->assignRole('admin');
    }
}
