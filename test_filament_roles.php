<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "=== TEST FILAMENT ROLES ===\n\n";
    
    // Test du UserResource
    echo "1. Test du UserResource:\n";
    $userResource = new App\Filament\Resources\UserResource();
    echo "✓ UserResource instancié\n";
    
    // Test du formulaire
    echo "\n2. Test du formulaire UserResource:\n";
    $form = App\Filament\Resources\UserResource::form(
        \Filament\Forms\Form::make()
    );
    echo "✓ Formulaire créé\n";
    
    // Vérifier les composants du formulaire
    $schema = $form->getSchema();
    echo "Nombre de composants: " . count($schema) . "\n";
    
    foreach ($schema as $component) {
        $name = method_exists($component, 'getName') ? $component->getName() : 'Unknown';
        $type = get_class($component);
        echo "- {$name}: {$type}\n";
        
        if ($name === 'roles') {
            echo "  ✓ Composant 'roles' trouvé\n";
            if (method_exists($component, 'getRelationshipName')) {
                echo "  ✓ Relation: " . $component->getRelationshipName() . "\n";
            }
        }
    }
    
    // Test du tableau
    echo "\n3. Test du tableau UserResource:\n";
    $table = App\Filament\Resources\UserResource::table(
        \Filament\Tables\Table::make()
    );
    echo "✓ Tableau créé\n";
    
    $columns = $table->getColumns();
    echo "Nombre de colonnes: " . count($columns) . "\n";
    
    foreach ($columns as $column) {
        $name = method_exists($column, 'getName') ? $column->getName() : 'Unknown';
        $type = get_class($column);
        echo "- {$name}: {$type}\n";
    }
    
    // Test avec un utilisateur réel
    echo "\n4. Test avec un utilisateur réel:\n";
    $user = App\Models\User::with('roles')->first();
    if ($user) {
        echo "Utilisateur: {$user->email}\n";
        echo "Rôles (relation): " . $user->roles->pluck('name')->implode(', ') . "\n";
        echo "Rôles (méthode): " . $user->getRoleNames()->implode(', ') . "\n";
        
        // Test de la sérialisation pour Filament
        $rolesForSelect = $user->roles->pluck('name', 'id')->toArray();
        echo "Rôles pour select: " . json_encode($rolesForSelect) . "\n";
    }
    
    // Test des options du select
    echo "\n5. Test des options de rôles:\n";
    $roles = Spatie\Permission\Models\Role::pluck('name', 'id')->toArray();
    echo "Options disponibles: " . json_encode($roles) . "\n";
    
} catch (Exception $e) {
    echo "ERREUR: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
