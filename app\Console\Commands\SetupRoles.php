<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class SetupRoles extends Command
{
    protected $signature = 'setup:roles';
    protected $description = 'Setup roles and permissions for the application';

    public function handle()
    {
        $this->info('Setting up roles and permissions...');

        // Create roles
        $admin = Role::firstOrCreate(['name' => 'admin']);
        $superadmin = Role::firstOrCreate(['name' => 'superadmin']);
        $utilisateur = Role::firstOrCreate(['name' => 'utilisateur']);

        $this->info('Roles created: admin, superadmin, utilisateur');

        // Create permissions
        $permissions = [
            'view users',
            'create users',
            'edit users',
            'delete users',
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        $this->info('Permissions created: ' . implode(', ', $permissions));

        // Assign permissions to roles
        $admin->syncPermissions($permissions);
        $superadmin->syncPermissions($permissions);

        $this->info('Permissions assigned to admin and superadmin roles');

        // Create test admin user if it doesn't exist
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('password'),
            ]
        );

        $adminUser->assignRole('admin');

        $this->info('Admin user created/updated: <EMAIL> (password: password)');
        $this->info('Setup completed successfully!');

        return 0;
    }
}
