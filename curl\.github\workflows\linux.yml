# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# SPDX-License-Identifier: curl

name: Linux

'on':
  push:
    branches:
      - master
      - '*/ci'
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'
  pull_request:
    branches:
      - master
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: {}

env:
  MAKEFLAGS: -j 5
  CURL_CI: github
  CURL_CLANG_TIDYFLAGS: '-checks=-clang-analyzer-security.insecureAPI.bzero,-clang-analyzer-security.insecureAPI.strcpy,-clang-analyzer-optin.performance.Padding,-clang-analyzer-security.insecureAPI.DeprecatedOrUnsafeBufferHandling,-clang-analyzer-valist.Uninitialized'
  # renovate: datasource=github-tags depName=libressl-portable/portable versioning=semver registryUrl=https://github.com
  LIBRESSL_VERSION: 4.1.0
  # renovate: datasource=github-tags depName=wolfSSL/wolfssl versioning=semver extractVersion=^v?(?<version>.+)-stable$ registryUrl=https://github.com
  WOLFSSL_VERSION: 5.8.0
  # renovate: datasource=github-tags depName=wolfSSL/wolfssh versioning=semver extractVersion=^v?(?<version>.+)-stable$ registryUrl=https://github.com
  WOLFSSH_VERSION: 1.4.19
  # renovate: datasource=github-tags depName=Mbed-TLS/mbedtls versioning=semver registryUrl=https://github.com
  MBEDTLS_VERSION: 3.6.3
  # renovate: datasource=github-tags depName=nibanks/msh3 versioning=semver registryUrl=https://github.com
  MSH3_VERSION: 0.6.0
  # renovate: datasource=github-tags depName=awslabs/aws-lc versioning=semver registryUrl=https://github.com
  AWSLC_VERSION: 1.52.0
  # handled in renovate.json
  OPENSSL_VERSION: 3.5.0
  # handled in renovate.json
  QUICTLS_VERSION: 3.3.0
  # renovate: datasource=github-tags depName=rustls/rustls-ffi versioning=semver registryUrl=https://github.com
  RUSTLS_VERSION: 0.15.0

jobs:
  linux:
    name: ${{ matrix.build.generate && 'CM' || 'AM' }} ${{ matrix.build.name }}
    runs-on: ${{ matrix.build.image || 'ubuntu-latest' }}
    container: ${{ matrix.build.container }}
    timeout-minutes: 45
    env:
      MATRIX_BUILD: ${{ matrix.build.generate && 'cmake' || 'autotools' }}
      MATRIX_INSTALL_PACKAGES: '${{ matrix.build.install_packages }}'
      MATRIX_INSTALL_STEPS: '${{ matrix.build.install_steps }}'
      MATRIX_MAKE_PREFIX: '${{ matrix.build.make-prefix }}'
    strategy:
      fail-fast: false
      matrix:
        build:
          - name: libressl heimdal
            install_packages: zlib1g-dev libnghttp2-dev libldap-dev heimdal-dev
            install_steps: libressl pytest
            configure: LDFLAGS=-Wl,-rpath,/home/<USER>/libressl/lib --with-openssl=/home/<USER>/libressl --with-gssapi --enable-debug

          - name: libressl heimdal valgrind
            install_packages: zlib1g-dev libnghttp2-dev libldap-dev heimdal-dev valgrind
            install_steps: libressl
            generate: -DOPENSSL_ROOT_DIR=/home/<USER>/libressl -DCURL_USE_GSSAPI=ON -DENABLE_DEBUG=ON -DCURL_LIBCURL_VERSIONED_SYMBOLS=ON

          - name: libressl clang
            install_packages: zlib1g-dev clang
            install_steps: libressl
            configure: CC=clang LDFLAGS=-Wl,-rpath,/home/<USER>/libressl/lib --with-openssl=/home/<USER>/libressl --enable-debug

          - name: wolfssl-all
            install_packages: zlib1g-dev
            install_steps: wolfssl-all
            configure: LDFLAGS=-Wl,-rpath,/home/<USER>/wolfssl-all/lib --with-wolfssl=/home/<USER>/wolfssl-all --enable-ech --enable-debug

          - name: wolfssl-opensslextra valgrind
            install_packages: zlib1g-dev valgrind
            install_steps: wolfssl-opensslextra wolfssh
            configure: LDFLAGS=-Wl,-rpath,/home/<USER>/wolfssl-opensslextra/lib --with-wolfssl=/home/<USER>/wolfssl-opensslextra --with-wolfssh=/home/<USER>/wolfssh --enable-ech --enable-debug

          - name: mbedtls valgrind
            install_packages: libnghttp2-dev libldap-dev valgrind
            install_steps: mbedtls
            configure: LDFLAGS=-Wl,-rpath,/home/<USER>/mbedtls/lib --with-mbedtls=/home/<USER>/mbedtls --enable-debug

          - name: mbedtls clang
            install_packages: libnghttp2-dev libldap-dev clang
            install_steps: mbedtls pytest
            configure: CC=clang LDFLAGS=-Wl,-rpath,/home/<USER>/mbedtls/lib --with-mbedtls=/home/<USER>/mbedtls --enable-debug

          - name: mbedtls
            install_packages: libnghttp2-dev
            install_steps: mbedtls
            PKG_CONFIG_PATH: /home/<USER>/mbedtls/lib/pkgconfig  # Requires v3.6.0 or v2.28.8
            generate: -DCURL_USE_MBEDTLS=ON -DENABLE_DEBUG=ON

          - name: mbedtls-pkg
            install_packages: libnghttp2-dev libmbedtls-dev
            generate: -DCURL_USE_MBEDTLS=ON -DENABLE_DEBUG=ON -DBUILD_LIBCURL_DOCS=OFF -DBUILD_MISC_DOCS=OFF -DENABLE_CURL_MANUAL=OFF

          - name: mbedtls-pkg !pc
            install_packages: libnghttp2-dev libmbedtls-dev
            install_steps: skipall
            generate: -DCURL_USE_MBEDTLS=ON -DENABLE_DEBUG=ON -DCURL_USE_PKGCONFIG=OFF -DCURL_COMPLETION_FISH=ON -DCURL_COMPLETION_ZSH=ON

          - name: msh3
            install_packages: zlib1g-dev
            install_steps: quictls msh3
            LDFLAGS: -Wl,-rpath,/home/<USER>/msh3/lib -Wl,-rpath,/home/<USER>/quictls/lib
            configure: --with-msh3=/home/<USER>/msh3 --with-openssl=/home/<USER>/quictls --enable-debug

          - name: msh3
            install_packages: zlib1g-dev
            install_steps: quictls msh3 skipall
            PKG_CONFIG_PATH: /home/<USER>/msh3/lib/pkgconfig  # Broken as of v0.6.0
            generate: -DOPENSSL_ROOT_DIR=/home/<USER>/quictls -DUSE_MSH3=ON -DMSH3_INCLUDE_DIR=/home/<USER>/msh3/include -DMSH3_LIBRARY=/home/<USER>/msh3/lib/libmsh3.so -DENABLE_DEBUG=ON

          - name: awslc
            install_packages: zlib1g-dev
            install_steps: awslc pytest
            configure: LDFLAGS=-Wl,-rpath,/home/<USER>/awslc/lib --with-openssl=/home/<USER>/awslc --enable-ech

          - name: awslc
            install_packages: zlib1g-dev
            install_steps: awslc
            generate: -DOPENSSL_ROOT_DIR=/home/<USER>/awslc -DUSE_ECH=ON -DCMAKE_UNITY_BUILD=OFF

          - name: openssl default
            install_steps: pytest
            configure: --with-openssl --enable-debug --disable-unity

          - name: openssl libssh2 sync-resolver valgrind
            install_packages: zlib1g-dev libssh2-1-dev libnghttp2-dev libldap-dev valgrind
            configure: --with-openssl --enable-debug --disable-threaded-resolver --with-libssh2

          - name: openssl
            install_packages: zlib1g-dev
            install_steps: pytest
            configure: CFLAGS=-std=gnu89 --with-openssl --enable-debug

          - name: openssl arm
            install_packages: zlib1g-dev
            install_steps: pytest
            configure: CFLAGS=-std=gnu89 --with-openssl --enable-debug
            image: 'ubuntu-24.04-arm'

          - name: openssl -O3 libssh valgrind
            install_packages: zlib1g-dev libssh-dev valgrind
            configure: CFLAGS=-O3 --with-openssl --enable-debug --with-libssh

          - name: openssl clang krb5
            install_packages: zlib1g-dev libkrb5-dev clang
            configure: CC=clang --with-openssl --with-gssapi --enable-debug --disable-docs --disable-manual

          - name: openssl clang krb5 LTO
            install_packages: zlib1g-dev libkrb5-dev clang
            install_steps: skipall
            generate: -DCURL_USE_OPENSSL=ON -DCURL_USE_GSSAPI=ON -DENABLE_DEBUG=ON -DCURL_LTO=ON

          - name: openssl !ipv6 !--libcurl !--digest-auth
            configure: --with-openssl --disable-ipv6 --enable-debug --disable-unity --disable-libcurl-option --disable-digest-auth

          - name: openssl https-only
            configure: >-
              --with-openssl --enable-debug --disable-unity
              --disable-dict --disable-gopher --disable-ldap --disable-telnet
              --disable-imap --disable-pop3 --disable-smtp
              --without-librtmp --disable-rtsp
              --without-libssh2 --without-libssh --without-wolfssh
              --disable-tftp --disable-ftp --disable-file --disable-smb

          - name: openssl torture !FTP
            install_packages: zlib1g-dev libnghttp2-dev libssh2-1-dev libc-ares-dev
            generate: -DCURL_USE_OPENSSL=ON -DENABLE_DEBUG=ON -DENABLE_ARES=ON
            tflags: -t --shallow=25 !FTP
            torture: true

          - name: openssl torture FTP
            install_packages: zlib1g-dev libnghttp2-dev libssh2-1-dev libc-ares-dev
            generate: -DCURL_USE_OPENSSL=ON -DENABLE_DEBUG=ON -DENABLE_ARES=ON
            tflags: -t --shallow=20 FTP
            torture: true

          - name: openssl i686
            install_packages: gcc-14-i686-linux-gnu libssl-dev:i386 librtmp-dev:i386 libssh2-1-dev:i386 libidn2-0-dev:i386 libc-ares-dev:i386 zlib1g-dev:i386
            configure: >-
              PKG_CONFIG_PATH=/usr/lib/i386-linux-gnu/pkgconfig
              CC=i686-linux-gnu-gcc-14
              CPPFLAGS=-I/usr/include/i386-linux-gnu
              LDFLAGS=-L/usr/lib/i386-linux-gnu
              --host=i686-linux-gnu
              --with-openssl --with-librtmp --with-libssh2 --with-libidn2 --enable-ares --enable-debug

          - name: '!ssl !http !smtp !imap'
            configure: --without-ssl --enable-debug --disable-http --disable-smtp --disable-imap --disable-unity

          - name: clang-tidy
            install_packages: clang-tidy zlib1g-dev libssl-dev libkrb5-dev
            install_steps: skipall wolfssl-opensslextra wolfssh
            configure: LDFLAGS=-Wl,-rpath,/home/<USER>/wolfssl-opensslextra/lib --with-wolfssl=/home/<USER>/wolfssl-opensslextra --with-wolfssh=/home/<USER>/wolfssh --with-openssl --enable-ech --with-gssapi --enable-ssls-export
            make-custom-target: tidy

          - name: scanbuild
            install_packages: clang-tools clang libssl-dev libssh2-1-dev
            install_steps: skipall
            configure: --with-openssl --enable-debug --with-libssh2 --disable-unity
            CC: clang
            configure-prefix: scan-build
            make-prefix: scan-build --status-bugs

          - name: address-sanitizer
            install_packages: zlib1g-dev libssh2-1-dev clang libssl-dev libubsan1 libasan8 libtsan2
            install_steps: pytest randcurl
            CFLAGS: -fsanitize=address,undefined,signed-integer-overflow -fno-sanitize-recover=undefined,integer -Wformat -Werror=format-security -Werror=array-bounds -g
            LDFLAGS: -fsanitize=address,undefined -fno-sanitize-recover=undefined,integer
            LIBS: -ldl -lubsan
            configure: CC=clang --with-openssl --enable-debug

          - name: thread-sanitizer
            install_packages: zlib1g-dev clang libtsan2
            install_steps: pytest openssl-tsan
            CFLAGS: -fsanitize=thread -g
            LDFLAGS: -fsanitize=thread -Wl,-rpath,/home/<USER>/openssl/lib
            configure: CC=clang --with-openssl=/home/<USER>/openssl --enable-debug

          - name: memory-sanitizer
            install_packages: clang
            install_steps: randcurl
            CFLAGS: -fsanitize=memory -Wformat -Werror=format-security -Werror=array-bounds -g
            LDFLAGS: -fsanitize=memory
            LIBS: -ldl
            configure: CC=clang --without-ssl --without-zlib --without-brotli --without-zstd --without-libpsl --without-nghttp2 --enable-debug

          - name: event-based
            install_packages: libssh-dev
            configure: --enable-debug --disable-shared --disable-threaded-resolver --with-libssh --with-openssl
            tflags: -n --test-event '!TLS-SRP'

          - name: duphandle
            install_packages: libssh-dev
            configure: --enable-debug --disable-shared --disable-threaded-resolver --with-libssh --with-openssl
            tflags: -n --test-duphandle '!TLS-SRP'

          - name: rustls valgrind
            install_packages: libnghttp2-dev libldap-dev valgrind
            install_steps: rust rustls
            configure: --with-rustls --enable-ech --enable-debug

          - name: rustls
            install_packages: libnghttp2-dev libldap-dev
            install_steps: rust rustls skipall pytest
            generate: -DCURL_USE_RUSTLS=ON -DUSE_ECH=ON -DENABLE_DEBUG=ON

          - name: IntelC openssl
            install_packages: zlib1g-dev libssl-dev
            install_steps: intel
            configure: CC=icc --enable-debug --with-openssl

          - name: Slackware openssl gssapi gcc
            # These are essentially the same flags used to build the curl Slackware package
            # https://ftpmirror.infania.net/slackware/slackware64-current/source/n/curl/curl.SlackBuild
            configure: --with-openssl --with-libssh2 --with-gssapi --enable-ares --enable-static=no --without-ca-bundle --with-ca-path=/etc/ssl/certs
            # Docker Hub image that `container-job` executes in
            container: 'andy5995/slackware-build-essential:15.0'

          - name: Alpine MUSL https-rr
            configure: --enable-debug --with-ssl --with-libssh2 --with-libidn2 --with-gssapi --enable-ldap --with-libpsl --enable-threaded-resolver --enable-ares --enable-httpsrr
            container: 'alpine:3.20'

          - name: Alpine MUSL c-ares https-rr
            configure: --enable-debug --with-ssl --with-libssh2 --with-libidn2 --with-gssapi --enable-ldap --with-libpsl --disable-threaded-resolver --enable-ares --enable-httpsrr --disable-unity
            container: 'alpine:3.20'

    steps:
      - name: 'install prereqs'
        if: ${{ matrix.build.container == null && !contains(matrix.build.name, 'i686') }}
        env:
          INSTALL_PACKAGES: >-
            ${{ !contains(matrix.build.install_steps, 'skipall') && !contains(matrix.build.install_steps, 'skiprun') && 'stunnel4' || '' }}
            ${{ contains(matrix.build.install_steps, 'pytest') && 'apache2 apache2-dev libnghttp2-dev vsftpd' || '' }}

        run: |
          sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
          sudo apt-get -o Dpkg::Use-Pty=0 update
          sudo rm -f /var/lib/man-db/auto-update
          sudo apt-get -o Dpkg::Use-Pty=0 install \
            libtool autoconf automake pkgconf \
            libpsl-dev libbrotli-dev libzstd-dev \
            ${INSTALL_PACKAGES} \
            ${MATRIX_INSTALL_PACKAGES}
          python3 -m venv ~/venv

      - name: 'install prereqs'
        if: ${{ contains(matrix.build.name, 'i686') }}
        run: |
          sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
          sudo dpkg --add-architecture i386
          sudo apt-get -o Dpkg::Use-Pty=0 update
          sudo rm -f /var/lib/man-db/auto-update
          sudo apt-get -o Dpkg::Use-Pty=0 install \
            libtool autoconf automake pkgconf stunnel4 \
            libpsl-dev:i386 libbrotli-dev:i386 libzstd-dev:i386 \
            ${MATRIX_INSTALL_PACKAGES}
          python3 -m venv ~/venv

      - name: 'install dependencies'
        if: ${{ startsWith(matrix.build.container, 'alpine') }}
        run: |
          apk add --no-cache build-base autoconf automake libtool perl openssl-dev \
            libssh2-dev zlib-dev brotli-dev zstd-dev libidn2-dev openldap-dev \
            heimdal-dev libpsl-dev c-ares-dev \
            py3-impacket py3-asn1 py3-six py3-pycryptodomex \
            perl-time-hires openssh stunnel sudo git openssl

      - name: 'cache libressl'
        if: ${{ contains(matrix.build.install_steps, 'libressl') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-libressl
        env:
          cache-name: cache-libressl
        with:
          path: ~/libressl
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.LIBRESSL_VERSION }}

      - name: 'build libressl'
        if: ${{ contains(matrix.build.install_steps, 'libressl') && steps.cache-libressl.outputs.cache-hit != 'true' }}
        run: |
          curl -LOsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/libressl/portable/releases/download/v${LIBRESSL_VERSION}/libressl-${LIBRESSL_VERSION}.tar.gz" | tar -xz
          cd "libressl-${LIBRESSL_VERSION}"
          ./configure --disable-dependency-tracking --prefix=/home/<USER>/libressl
          make install

      - name: 'cache wolfssl (all)'
        if: ${{ contains(matrix.build.install_steps, 'wolfssl-all') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-wolfssl-all
        env:
          cache-name: cache-wolfssl-all
        with:
          path: ~/wolfssl-all
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.WOLFSSL_VERSION }}

      - name: 'build wolfssl (all)'  # does not support `OPENSSL_COEXIST`
        if: ${{ contains(matrix.build.install_steps, 'wolfssl-all') && steps.cache-wolfssl-all.outputs.cache-hit != 'true' }}
        run: |
          curl -LOsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/wolfSSL/wolfssl/archive/v${WOLFSSL_VERSION}-stable.tar.gz" | tar -xz
          cd "wolfssl-${WOLFSSL_VERSION}-stable"
          ./autogen.sh
          ./configure --disable-dependency-tracking --enable-tls13 --enable-harden --enable-all \
            --disable-benchmark --disable-crypttests --disable-examples --prefix=/home/<USER>/wolfssl-all
          make install

      - name: 'cache wolfssl (opensslextra)'  # does support `OPENSSL_COEXIST`
        if: ${{ contains(matrix.build.install_steps, 'wolfssl-opensslextra') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-wolfssl-opensslextra
        env:
          cache-name: cache-wolfssl-opensslextra
        with:
          path: ~/wolfssl-opensslextra
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.WOLFSSL_VERSION }}

      - name: 'build wolfssl (opensslextra)'
        if: ${{ contains(matrix.build.install_steps, 'wolfssl-opensslextra') && steps.cache-wolfssl-opensslextra.outputs.cache-hit != 'true' }}
        run: |
          curl -LOsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/wolfSSL/wolfssl/archive/v${WOLFSSL_VERSION}-stable.tar.gz" | tar -xz
          cd "wolfssl-${WOLFSSL_VERSION}-stable"
          ./autogen.sh
          ./configure --disable-dependency-tracking --enable-tls13 --enable-harden --enable-wolfssh --enable-ech --enable-opensslextra \
            --disable-benchmark --disable-crypttests --disable-examples --prefix=/home/<USER>/wolfssl-opensslextra
          make install

      - name: 'cache wolfssh'
        if: ${{ contains(matrix.build.install_steps, 'wolfssh') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-wolfssh
        env:
          cache-name: cache-wolfssh
        with:
          path: ~/wolfssh
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.WOLFSSH_VERSION }}-${{ env.WOLFSSL_VERSION }}

      - name: 'build wolfssh'
        if: ${{ contains(matrix.build.install_steps, 'wolfssh') && steps.cache-wolfssh.outputs.cache-hit != 'true' }}
        run: |
          curl -LOsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/wolfSSL/wolfssh/archive/v${WOLFSSH_VERSION}-stable.tar.gz" | tar -xz
          cd "wolfssh-${WOLFSSH_VERSION}-stable"
          ./autogen.sh
          ./configure --disable-dependency-tracking --with-wolfssl=/home/<USER>/wolfssl-opensslextra --enable-scp --enable-sftp --disable-term \
            --disable-examples --prefix=/home/<USER>/wolfssh
          make install

      - name: 'cache mbedtls'
        if: ${{ contains(matrix.build.install_steps, 'mbedtls') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-mbedtls
        env:
          cache-name: cache-mbedtls-threadsafe
        with:
          path: ~/mbedtls
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.MBEDTLS_VERSION }}

      - name: 'build mbedtls'
        if: ${{ contains(matrix.build.install_steps, 'mbedtls') && steps.cache-mbedtls.outputs.cache-hit != 'true' }}
        run: |
          curl -LOsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/Mbed-TLS/mbedtls/releases/download/mbedtls-${MBEDTLS_VERSION}/mbedtls-${MBEDTLS_VERSION}.tar.bz2" | tar -xj
          cd "mbedtls-${MBEDTLS_VERSION}"
          ./scripts/config.py set MBEDTLS_THREADING_C
          ./scripts/config.py set MBEDTLS_THREADING_PTHREAD
          cmake -B . -G Ninja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_POSITION_INDEPENDENT_CODE=ON -DCMAKE_INSTALL_PREFIX=/home/<USER>/mbedtls \
            -DENABLE_PROGRAMS=OFF -DENABLE_TESTING=OFF
          cmake --build .
          cmake --install .

      - name: 'cache openssl (thread sanitizer)'
        if: ${{ contains(matrix.build.install_steps, 'openssl-tsan') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-openssl-tsan
        env:
          cache-name: cache-openssl-tsan
        with:
          path: ~/openssl
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.OPENSSL_VERSION }}

      - name: 'build openssl (thread sanitizer)'
        if: ${{ contains(matrix.build.install_steps, 'openssl-tsan') && steps.cache-openssl-tsan.outputs.cache-hit != 'true' }}
        run: |
          git clone --quiet --depth=1 -b "openssl-${OPENSSL_VERSION}" https://github.com/openssl/openssl
          cd openssl
          CC=clang CFLAGS='-fsanitize=thread' LDFLAGS='-fsanitize=thread' ./config --prefix=/home/<USER>/openssl --libdir=lib no-makedepend no-apps no-docs no-tests
          make
          make -j1 install_sw

      - name: 'cache quictls'
        if: ${{ contains(matrix.build.install_steps, 'quictls') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-quictls
        env:
          cache-name: cache-quictls
        with:
          path: ~/quictls
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.QUICTLS_VERSION }}-quic1

      - name: 'build quictls'
        if: ${{ contains(matrix.build.install_steps, 'quictls') && steps.cache-quictls.outputs.cache-hit != 'true' }}
        run: |
          git clone --quiet --depth=1 -b "openssl-${QUICTLS_VERSION}-quic1" https://github.com/quictls/openssl
          cd openssl
          ./config --prefix=/home/<USER>/quictls --libdir=lib no-makedepend no-apps no-docs no-tests
          make
          make -j1 install_sw

      - name: 'cache msh3'
        if: ${{ contains(matrix.build.install_steps, 'msh3') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-msh3
        env:
          cache-name: cache-msh3
        with:
          path: ~/msh3
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.MSH3_VERSION }}

      - name: 'build msh3'
        if: ${{ contains(matrix.build.install_steps, 'msh3') && steps.cache-msh3.outputs.cache-hit != 'true' }}
        run: |
          git clone --quiet --depth=1 -b "v${MSH3_VERSION}" --recursive https://github.com/nibanks/msh3
          cd msh3
          cmake -B . -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=/home/<USER>/msh3
          cmake --build .
          cmake --install .

      - name: 'cache awslc'
        if: ${{ contains(matrix.build.install_steps, 'awslc') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-awslc
        env:
          cache-name: cache-awslc
        with:
          path: ~/awslc
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.AWSLC_VERSION }}

      - name: 'build awslc'
        if: ${{ contains(matrix.build.install_steps, 'awslc') && steps.cache-awslc.outputs.cache-hit != 'true' }}
        run: |
          curl -LOsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/awslabs/aws-lc/archive/refs/tags/v${AWSLC_VERSION}.tar.gz" | tar -xz
          mkdir "aws-lc-${AWSLC_VERSION}-build"
          cd "aws-lc-${AWSLC_VERSION}-build"
          cmake -G Ninja -DCMAKE_INSTALL_PREFIX=/home/<USER>/awslc "../aws-lc-${AWSLC_VERSION}" -DBUILD_TOOL=OFF -DBUILD_TESTING=OFF
          cmake --build .
          cmake --install .

      - name: 'cache rustls'
        if: ${{ contains(matrix.build.install_steps, 'rustls') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-rustls
        env:
          cache-name: cache-rustls
        with:
          path: ~/rustls
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ env.RUSTLS_VERSION }}

      - name: 'fetch rustls deb'
        if: ${{ contains(matrix.build.install_steps, 'rustls') && steps.cache-rustls.outputs.cache-hit != 'true' }}
        run: |
          mkdir -p ~/rustls
          curl -LsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/rustls/rustls-ffi/releases/download/v${RUSTLS_VERSION}/librustls_${RUSTLS_VERSION}_amd64.deb.zip" -o ~/rustls/librustls.zip
          unzip ~/rustls/librustls.zip -d ~/rustls
          rm ~/rustls/librustls.zip

      - name: 'build rustls'
        # Note: we don't check cache-hit here. If the cache is hit, we still need to dpkg install the deb.
        if: ${{ contains(matrix.build.install_steps, 'rustls') }}
        run: sudo dpkg -i ~/rustls/"librustls_${RUSTLS_VERSION}_amd64.deb"

      - name: 'install Intel compilers'
        if: ${{ contains(matrix.build.install_steps, 'intel') }}
        run: |
          curl -sSf --compressed https://apt.repos.intel.com/intel-gpg-keys/GPG-PUB-KEY-INTEL-SW-PRODUCTS.PUB | sudo tee /etc/apt/trusted.gpg.d/intel-sw.asc >/dev/null
          sudo add-apt-repository "deb https://apt.repos.intel.com/oneapi all main"
          sudo apt-get -o Dpkg::Use-Pty=0 install intel-oneapi-compiler-dpcpp-cpp-and-cpp-classic
          source /opt/intel/oneapi/setvars.sh
          printenv >> "$GITHUB_ENV"

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'autoreconf'
        if: ${{ matrix.build.configure }}
        run: autoreconf -fi

      - name: 'configure'
        env:
          CC: '${{ matrix.build.CC }}'
          CFLAGS: '${{ matrix.build.CFLAGS }}'
          LDFLAGS: '${{ matrix.build.LDFLAGS }}'
          LIBS: '${{ matrix.build.LIBS }}'
          MATRIX_CONFIGURE: '${{ matrix.build.configure }}'
          MATRIX_CONFIGURE_PREFIX: '${{ matrix.build.configure-prefix }}'
          MATRIX_GENERATE: '${{ matrix.build.generate }}'
          MATRIX_PKG_CONFIG_PATH: '${{ matrix.build.PKG_CONFIG_PATH }}'
        run: |
          [[ "${MATRIX_INSTALL_STEPS}" = *'awslc'* ]] && sudo apt-get -o Dpkg::Use-Pty=0 purge libssl-dev
          [ -n "${MATRIX_PKG_CONFIG_PATH}" ] && export PKG_CONFIG_PATH="${MATRIX_PKG_CONFIG_PATH}"
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake -B bld -G Ninja \
              -DCMAKE_INSTALL_PREFIX="$HOME"/curl-install \
              -DCMAKE_C_COMPILER_TARGET="$(uname -m)-pc-linux-gnu" -DBUILD_STATIC_LIBS=ON \
              -DCMAKE_UNITY_BUILD=ON -DCURL_WERROR=ON \
              ${MATRIX_GENERATE}
          else
            mkdir bld && cd bld && \
            ${MATRIX_CONFIGURE_PREFIX} \
            ../configure --enable-unity --enable-warnings --enable-werror \
              --disable-dependency-tracking \
              ${MATRIX_CONFIGURE}
          fi

      - name: 'configure log'
        if: ${{ !cancelled() }}
        run: cat bld/config.log bld/CMakeFiles/CMakeConfigureLog.yaml 2>/dev/null || true

      - name: 'curl_config.h'
        run: |
          echo '::group::raw'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld/lib/curl_config.h | sort || true

      - name: 'test configs'
        run: grep -H -v '^#' bld/tests/config bld/tests/http/config.ini || true

      - name: 'build'
        env:
          MATRIX_MAKE_CUSTOM_TARGET: '${{ matrix.build.make-custom-target }}'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            ${MATRIX_MAKE_PREFIX} cmake --build bld --verbose
          else
            ${MATRIX_MAKE_PREFIX} make -C bld V=1 ${MATRIX_MAKE_CUSTOM_TARGET}
          fi

      - name: 'single-use function check'
        if: ${{ contains(matrix.build.configure, '--disable-unity') || contains(matrix.build.generate, '-DCMAKE_UNITY_BUILD=OFF') }}
        run: |
          git config --global --add safe.directory "*"
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            libcurla=bld/lib/libcurl.a
          else
            libcurla=bld/lib/.libs/libcurl.a
          fi
          ./scripts/singleuse.pl --unit "${libcurla}"

      - name: 'check curl -V output'
        if: ${{ matrix.build.make-custom-target != 'tidy' }}
        run: bld/src/curl -V

      - name: 'cmake install'
        if: ${{ matrix.build.generate }}
        run: cmake --install bld --strip

      - name: 'build tests'
        if: ${{ !contains(matrix.build.install_steps, 'skipall') }}
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose --target testdeps
          else
            make -C bld V=1 -C tests
          fi

      - name: 'install test prereqs'
        if: ${{ !contains(matrix.build.install_steps, 'skipall') && matrix.build.container == null }}
        run: |
          [ -x ~/venv/bin/activate ] && source ~/venv/bin/activate
          python3 -m pip install -r tests/requirements.txt

      - name: 'run tests'
        if: ${{ !contains(matrix.build.install_steps, 'skipall') && !contains(matrix.build.install_steps, 'skiprun') }}
        timeout-minutes: ${{ contains(matrix.build.install_packages, 'valgrind') && 30 || 15 }}
        env:
          TEST_TARGET: ${{ matrix.build.torture && 'test-torture' || 'test-ci' }}
          TFLAGS: '${{ matrix.build.tflags }}'
        run: |
          if [ "${TEST_TARGET}" = 'test-ci' ]; then
            if [[ "${MATRIX_INSTALL_STEPS}" = *'wolfssh'* ]]; then
              TFLAGS+=' ~SFTP'  # curl: (79) wolfssh SFTP connect error -1051 / WS_MATCH_KEY_ALGO_E / cannot match key algo with peer
            fi
            if [[ "${MATRIX_INSTALL_PACKAGES}" = *'valgrind'* ]]; then
              TFLAGS+=' -j6'
            fi
            if [[ "${MATRIX_INSTALL_PACKAGES}" = *'heimdal-dev'* ]]; then
              TFLAGS+=' ~2077 ~2078'  # valgrind reporting memory leaks from Curl_auth_decode_spnego_message() -> gss_import_name()
            fi
          fi
          [ -x ~/venv/bin/activate ] && source ~/venv/bin/activate
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose --target "${TEST_TARGET}"
          else
            make -C bld V=1 "${TEST_TARGET}"
          fi

      - name: 'install pytest prereqs'
        if: ${{ contains(matrix.build.install_steps, 'pytest') }}
        run: |
          [ -x ~/venv/bin/activate ] && source ~/venv/bin/activate
          python3 -m pip install -r tests/http/requirements.txt

      - name: 'run pytest'
        if: ${{ contains(matrix.build.install_steps, 'pytest') }}
        env:
          PYTEST_ADDOPTS: '--color=yes'
          PYTEST_XDIST_AUTO_NUM_WORKERS: 4
        run: |
          [ -x ~/venv/bin/activate ] && source ~/venv/bin/activate
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose --target curl-pytest-ci
          else
            make -C bld V=1 pytest-ci
          fi

      - name: 'randcurl'
        if: ${{ contains(matrix.build.install_steps, 'randcurl') }}
        run: |
          mkdir run
          cd run
          ../.github/scripts/randcurl.pl 60 ../bld/src/curl

      - name: 'build examples'
        if: ${{ matrix.build.make-custom-target != 'tidy' }}
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            ${MATRIX_MAKE_PREFIX} cmake --build bld --verbose --target curl-examples
          else
            ${MATRIX_MAKE_PREFIX} make -C bld V=1 examples
          fi
