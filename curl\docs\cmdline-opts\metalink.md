---
c: Copyright (C) <PERSON>, <<EMAIL>>, et al.
SPDX-License-Identifier: curl
Long: metalink
Help: Process given URLs as metalink XML file
Added: 7.27.0
Category: deprecated
Multi: single
See-also:
  - parallel
Example:
  - --metalink file $URL
---

# `--metalink`

This option was previously used to specify a Metalink resource. Metalink
support is disabled in curl for security reasons (added in 7.78.0).
