<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "=== TEST FORMULAIRE UTILISATEUR ===\n\n";
    
    // Test des options de rôles
    echo "1. Options de rôles disponibles:\n";
    $roles = \Spatie\Permission\Models\Role::pluck('name', 'id')->toArray();
    foreach ($roles as $id => $name) {
        echo "- ID: {$id}, Nom: {$name}\n";
    }
    echo "\n";
    
    // Test avec un utilisateur existant
    echo "2. Test avec utilisateur existant:\n";
    $user = App\Models\User::with('roles')->first();
    if ($user) {
        echo "Utilisateur: {$user->email}\n";
        echo "Rôles actuels (IDs): " . $user->roles->pluck('id')->implode(', ') . "\n";
        echo "Rôles actuels (noms): " . $user->roles->pluck('name')->implode(', ') . "\n";
        
        // Simuler ce que Filament devrait recevoir
        $defaultRoles = $user->roles->pluck('id')->toArray();
        echo "Valeur par défaut pour le formulaire: " . json_encode($defaultRoles) . "\n";
    }
    echo "\n";
    
    // Test de synchronisation des rôles
    echo "3. Test de synchronisation des rôles:\n";
    if ($user) {
        $originalRoles = $user->roles->pluck('name')->toArray();
        echo "Rôles avant: " . implode(', ', $originalRoles) . "\n";
        
        // Simuler l'ajout d'un rôle
        $allRoleIds = \Spatie\Permission\Models\Role::pluck('id')->toArray();
        $user->syncRoles($allRoleIds);
        $user->refresh();
        
        $newRoles = $user->roles->pluck('name')->toArray();
        echo "Rôles après sync avec tous les IDs: " . implode(', ', $newRoles) . "\n";
        
        // Remettre les rôles originaux
        $originalRoleIds = \Spatie\Permission\Models\Role::whereIn('name', $originalRoles)->pluck('id')->toArray();
        $user->syncRoles($originalRoleIds);
        $user->refresh();
        
        echo "Rôles restaurés: " . $user->roles->pluck('name')->implode(', ') . "\n";
    }
    
} catch (Exception $e) {
    echo "ERREUR: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
