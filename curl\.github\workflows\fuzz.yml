# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# SPDX-License-Identifier: curl

name: Fuzzer

'on':
  push:
    branches:
      - master
      - '*/ci'
    paths-ignore:
      - '**/*.md'
      - '**/CMakeLists.txt'
      - '.circleci/**'
      - 'appveyor.*'
      - 'CMake/**'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'tests/data/**'
      - 'winbuild/**'
  pull_request:
    branches:
      - master
    paths-ignore:
      - '**/*.md'
      - '**/CMakeLists.txt'
      - '.circleci/**'
      - 'appveyor.*'
      - 'CMake/**'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'tests/data/**'
      - 'winbuild/**'

permissions: {}

jobs:
  Fuzzing:
    uses: curl/curl-fuzzer/.github/workflows/ci.yml@master
