# Copyright (C) <PERSON>
#
# SPDX-License-Identifier: curl

name: non-native

'on':
  push:
    branches:
      - master
      - '*/ci'
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'
  pull_request:
    branches:
      - master
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: {}

env:
  CURL_CI: github

jobs:
  netbsd:
    name: 'NetBSD, CM clang openssl ${{ matrix.arch }}'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        arch: ['x86_64']
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
      - name: 'cmake'
        uses: cross-platform-actions/action@97419d18f6470332677e345e9df97cdc71244ead # v0.28.0
        env:
          MATRIX_ARCH: '${{ matrix.arch }}'
        with:
          environment_variables: MATRIX_ARCH
          operating_system: 'netbsd'
          version: '10.1'
          architecture: ${{ matrix.arch }}
          run: |
            # https://pkgsrc.se/
            time sudo pkgin -y install cmake ninja-build pkg-config perl brotli heimdal openldap-client libssh2 libidn2 libpsl nghttp2 py311-impacket
            time cmake -B bld -G Ninja \
              -DCMAKE_INSTALL_PREFIX="$HOME"/curl-install \
              -DCMAKE_UNITY_BUILD=ON \
              -DCURL_WERROR=ON \
              -DENABLE_DEBUG=ON -DCMAKE_BUILD_TYPE=Debug \
              -DCURL_USE_OPENSSL=ON \
              -DCURL_USE_GSSAPI=ON \
              || { cat bld/CMakeFiles/CMake*.yaml; false; }
            echo '::group::curl_config.h (raw)'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
            echo '::group::curl_config.h'; grep -F '#define' bld/lib/curl_config.h | sort || true; echo '::endgroup::'
            time cmake --build bld
            time cmake --install bld
            bld/src/curl --disable --version
            if [ "${MATRIX_ARCH}" = 'x86_64' ]; then  # Slow on emulated CPU
              time cmake --build bld --target testdeps
              export TFLAGS='-j8'
              time cmake --build bld --target test-ci
            fi
            echo '::group::build examples'
            time cmake --build bld --target curl-examples
            echo '::endgroup::'

  openbsd:
    name: 'OpenBSD, CM clang libressl ${{ matrix.arch }}'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        arch: ['x86_64']
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
      - name: 'cmake'
        uses: cross-platform-actions/action@97419d18f6470332677e345e9df97cdc71244ead # v0.28.0
        env:
          MATRIX_ARCH: '${{ matrix.arch }}'
        with:
          environment_variables: MATRIX_ARCH
          operating_system: 'openbsd'
          version: '7.7'
          architecture: ${{ matrix.arch }}
          run: |
            # https://openbsd.app/
            # https://www.openbsd.org/faq/faq15.html
            time sudo pkg_add cmake ninja brotli openldap-client-- libssh2 libidn2 libpsl nghttp2 py3-six py3-impacket
            time cmake -B bld -G Ninja \
              -DCMAKE_INSTALL_PREFIX="$HOME"/curl-install \
              -DCMAKE_UNITY_BUILD=ON \
              -DCURL_WERROR=ON \
              -DENABLE_DEBUG=ON -DCMAKE_BUILD_TYPE=Debug \
              -DCURL_USE_OPENSSL=ON \
              || { cat bld/CMakeFiles/CMake*.yaml; false; }
            echo '::group::curl_config.h (raw)'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
            echo '::group::curl_config.h'; grep -F '#define' bld/lib/curl_config.h | sort || true; echo '::endgroup::'
            time cmake --build bld
            time cmake --install bld
            bld/src/curl --disable --version
            if [ "${MATRIX_ARCH}" = 'x86_64' ]; then  # Slow on emulated CPU
              time cmake --build bld --target testdeps
              export TFLAGS='-j8 !2707'  # Skip 2707 'ws: Peculiar frame sizes' on suspicion of hangs
              time cmake --build bld --target test-ci
            fi
            echo '::group::build examples'
            time cmake --build bld --target curl-examples
            echo '::endgroup::'

  freebsd:
    name: "FreeBSD, ${{ matrix.build == 'cmake' && 'CM' || 'AM' }} ${{ matrix.compiler }} openssl${{ matrix.desc }} ${{ matrix.arch }}"
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        include:
          - { build: 'autotools', arch: 'x86_64', compiler: 'clang' }
          - { build: 'cmake'    , arch: 'x86_64', compiler: 'clang', options: '-DCMAKE_UNITY_BUILD=OFF', desc: ' !unity !runtests !examples' }
          - { build: 'autotools', arch: 'arm64', compiler: 'clang' }
          - { build: 'cmake'    , arch: 'arm64', compiler: 'clang' }
      fail-fast: false
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
      - name: '${{ matrix.build }}'
        uses: cross-platform-actions/action@97419d18f6470332677e345e9df97cdc71244ead # v0.28.0
        env:
          CC: '${{ matrix.compiler }}'
          MATRIX_ARCH: '${{ matrix.arch }}'
          MATRIX_BUILD: '${{ matrix.build }}'
          MATRIX_DESC: '${{ matrix.desc }}'
          MATRIX_OPTIONS: '${{ matrix.options }}'
        with:
          environment_variables: CC MATRIX_ARCH MATRIX_BUILD MATRIX_DESC MATRIX_OPTIONS
          operating_system: 'freebsd'
          version: '14.2'
          architecture: ${{ matrix.arch }}
          run: |
            export CURL_CI=github

            # https://ports.freebsd.org/
            if [ "${MATRIX_BUILD}" = 'cmake' ]; then
              time sudo pkg install -y cmake-core ninja perl5 \
                pkgconf brotli openldap26-client libidn2 libnghttp2 stunnel py311-impacket
            else
              time sudo pkg install -y autoconf automake libtool \
                pkgconf brotli openldap26-client libidn2 libnghttp2 stunnel py311-impacket
              export MAKEFLAGS=-j3
            fi

            if [ "${MATRIX_BUILD}" = 'cmake' ]; then
              time cmake -B bld -G Ninja \
                -DCMAKE_INSTALL_PREFIX="$HOME"/curl-install \
                -DCMAKE_C_COMPILER="${CC}" \
                -DCMAKE_UNITY_BUILD=ON \
                -DCURL_WERROR=ON \
                -DENABLE_DEBUG=ON -DCMAKE_BUILD_TYPE=Debug \
                -DCURL_USE_OPENSSL=ON \
                -DCURL_USE_GSSAPI=ON \
                ${MATRIX_OPTIONS} \
                || { cat bld/CMakeFiles/CMake*.yaml; false; }
            else
              time autoreconf -fi
              if [ "${MATRIX_ARCH}" != 'x86_64' ]; then
                options='--disable-manual --disable-docs'  # Slow with autotools, skip on emulated CPU
              fi
              mkdir bld && cd bld
              time ../configure --enable-unity --enable-debug --enable-warnings --enable-werror \
                --prefix="$HOME"/curl-install \
                --with-openssl \
                --with-brotli --enable-ldap --enable-ldaps --with-libidn2 --with-libssh2 --with-nghttp2 --with-gssapi \
                --disable-dependency-tracking \
                ${options} \
                ${MATRIX_OPTIONS} \
                || { tail -n 1000 config.log; false; }
              cd ..
            fi

            echo '::group::curl_config.h (raw)'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
            echo '::group::curl_config.h'; grep -F '#define' bld/lib/curl_config.h | sort || true; echo '::endgroup::'

            if [ "${MATRIX_BUILD}" = 'cmake' ]; then
              time cmake --build bld
              time cmake --install bld
            else
              time make -C bld install
            fi

            bld/src/curl --disable --version

            if [ "${MATRIX_ARCH}" = 'x86_64' ]; then  # Slow on emulated CPU
              if [ "${MATRIX_BUILD}" = 'cmake' ]; then
                time cmake --build bld --target testdeps
              else
                time make -C bld -C tests
              fi
              if [ "${MATRIX_DESC#*!runtests*}" = "${MATRIX_DESC}" ]; then
                export TFLAGS='-j8'
                if [ "${MATRIX_BUILD}" = 'cmake' ]; then
                  time cmake --build bld --verbose --target test-ci
                else
                  time make -C bld V=1 test-ci
                fi
              fi
            fi

            if [ "${MATRIX_DESC#*!examples*}" = "${MATRIX_DESC}" ]; then
              echo '::group::build examples'
              if [ "${MATRIX_BUILD}" = 'cmake' ]; then
                time cmake --build bld --target curl-examples
              else
                time make -C bld examples
              fi
              echo '::endgroup::'
            fi

  omnios:
    name: 'OmniOS, AM gcc openssl amd64'
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
      - name: 'autotools'
        uses: vmactions/omnios-vm@8eba2a9217262f275d4566751a92d6ef2f433d00 # v1
        with:
          usesh: true
          # https://pkg.omnios.org/r151052/core/en/index.shtml
          prepare: pkg install build-essential libtool nghttp2
          run: |
            set -e
            ln -s /usr/bin/gcpp /usr/bin/cpp  # Some tests expect `cpp`, which is named `gcpp` in this env.
            export MAKEFLAGS=-j3
            export CURL_CI=github
            time autoreconf -fi
            mkdir bld && cd bld
            time ../configure --enable-unity --enable-debug --enable-warnings --enable-werror \
              --prefix="$HOME"/curl-install \
              --with-openssl \
              --disable-dependency-tracking \
              || { tail -n 1000 config.log; false; }
            echo '::group::curl_config.h (raw)'; cat lib/curl_config.h || true; echo '::endgroup::'
            echo '::group::curl_config.h'; grep -F '#define' lib/curl_config.h | sort || true; echo '::endgroup::'
            time gmake install
            src/curl --disable --version
            time gmake -C tests
            time gmake test-ci V=1
            echo '::group::build examples'
            time gmake examples
            echo '::endgroup::'

  ios:
    name: "iOS, ${{ (matrix.build.generator && format('CM-{0}', matrix.build.generator)) || (matrix.build.generate && 'CM' || 'AM' )}} ${{ matrix.build.name }} arm64"
    runs-on: 'macos-latest'
    timeout-minutes: 10
    env:
      MAKEFLAGS: -j 4
      DEVELOPER_DIR: "/Applications/Xcode${{ matrix.build.xcode && format('_{0}', matrix.build.xcode) || '' }}.app/Contents/Developer"
      CC: ${{ matrix.build.compiler || 'clang' }}
      MATRIX_BUILD: ${{ matrix.build.generate && 'cmake' || 'autotools' }}
      MATRIX_OPTIONS: ${{ matrix.build.options }}
      # renovate: datasource=github-tags depName=libressl-portable/portable versioning=semver registryUrl=https://github.com
      LIBRESSL_VERSION: 4.1.0
    strategy:
      fail-fast: false
      matrix:
        build:
          - name: 'libressl'
            install_steps: libressl
            configure: --with-openssl=/Users/<USER>/libressl --without-libpsl

          - name: 'libressl'
            install_steps: libressl
            # FIXME: Could not make OPENSSL_ROOT_DIR work. CMake seems to prepend sysroot to it.
            generate: >-
              -DCMAKE_BUILD_TYPE=Release -DCMAKE_UNITY_BUILD_BATCH_SIZE=50
              -DOPENSSL_INCLUDE_DIR=/Users/<USER>/libressl/include
              -DOPENSSL_SSL_LIBRARY=/Users/<USER>/libressl/lib/libssl.a
              -DOPENSSL_CRYPTO_LIBRARY=/Users/<USER>/libressl/lib/libcrypto.a
              -DCURL_USE_LIBPSL=OFF

          - name: 'libressl'
            install_steps: libressl
            generator: Xcode
            options: --config Debug
            generate: >-
              -DCMAKE_XCODE_ATTRIBUTE_CODE_SIGNING_ALLOWED=OFF
              -DMACOSX_BUNDLE_GUI_IDENTIFIER=se.curl
              -DOPENSSL_INCLUDE_DIR=/Users/<USER>/libressl/include
              -DOPENSSL_SSL_LIBRARY=/Users/<USER>/libressl/lib/libssl.a
              -DOPENSSL_CRYPTO_LIBRARY=/Users/<USER>/libressl/lib/libcrypto.a
              -DCURL_USE_LIBPSL=OFF

    steps:
      - name: 'brew install'
        if: ${{ matrix.build.configure }}
        run: |
          # shellcheck disable=SC2181,SC2034
          while [[ $? == 0 ]]; do for i in 1 2 3; do if brew update && brew install automake libtool; then break 2; else echo Error: wait to try again; sleep 10; fi; done; false Too many retries; done

      - name: 'toolchain versions'
        run: |
          command -v "${CC}"; "${CC}" --version || true
          xcodebuild -version || true
          xcodebuild -sdk -version | grep '^Path:' || true
          xcrun --sdk iphoneos --show-sdk-path 2>/dev/null || true
          xcrun --sdk iphoneos --show-sdk-version || true
          echo '::group::macros predefined'; "${CC}" -dM -E - < /dev/null | sort || true; echo '::endgroup::'
          echo '::group::brew packages installed'; ls -l /opt/homebrew/opt; echo '::endgroup::'

      - name: 'cache libressl'
        if: ${{ contains(matrix.build.install_steps, 'libressl') }}
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-libressl
        env:
          cache-name: cache-libressl
        with:
          path: ~/libressl
          key: iOS-${{ env.cache-name }}-${{ env.LIBRESSL_VERSION }}

      - name: 'build libressl'
        if: ${{ contains(matrix.build.install_steps, 'libressl') && steps.cache-libressl.outputs.cache-hit != 'true' }}
        run: |
          curl -LsSf --retry 6 --retry-connrefused --max-time 999 \
            "https://github.com/libressl/portable/releases/download/v${LIBRESSL_VERSION}/libressl-${LIBRESSL_VERSION}.tar.gz" | tar -x
          cd "libressl-${LIBRESSL_VERSION}"
          # FIXME: on the 4.0.1 release, delete '-DHAVE_ENDIAN_H=0'
          cmake -B . -G Ninja \
            -DHAVE_ENDIAN_H=0 \
            -DCMAKE_INSTALL_PREFIX=/Users/<USER>/libressl \
            -DCMAKE_SYSTEM_NAME=iOS \
            -DCMAKE_SYSTEM_PROCESSOR=aarch64 \
            -DBUILD_SHARED_LIBS=OFF \
            -DLIBRESSL_APPS=OFF \
            -DLIBRESSL_TESTS=OFF
          cmake --build .
          cmake --install . --verbose

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'autoreconf'
        if: ${{ matrix.build.configure }}
        run: autoreconf -fi

      - name: 'configure'
        env:
          MATRIX_CONFIGURE: '${{ matrix.build.configure }}'
          MATRIX_GENERATE: '${{ matrix.build.generate }}'
          MATRIX_GENERATOR: '${{ matrix.build.generator }}'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            # https://cmake.org/cmake/help/latest/manual/cmake-toolchains.7.html#cross-compiling-for-ios-tvos-visionos-or-watchos
            [ -n "${MATRIX_GENERATOR}" ] && options="-G ${MATRIX_GENERATOR}"
            cmake -B bld -G Ninja -D_CURL_PREFILL=ON \
              -DCMAKE_UNITY_BUILD=ON -DCURL_WERROR=ON \
              -DCMAKE_SYSTEM_NAME=iOS \
              -DUSE_APPLE_IDN=ON \
              ${MATRIX_GENERATE} ${options}
          else
            mkdir bld && cd bld && ../configure --enable-unity --enable-warnings --enable-werror \
              --disable-dependency-tracking \
              CFLAGS="-isysroot $(xcrun --sdk iphoneos --show-sdk-path 2>/dev/null)" \
              --host=aarch64-apple-darwin \
              --with-apple-idn \
              ${MATRIX_CONFIGURE}
          fi

      - name: 'configure log'
        if: ${{ !cancelled() }}
        run: cat bld/config.log bld/CMakeFiles/CMakeConfigureLog.yaml 2>/dev/null || true

      - name: 'curl_config.h'
        run: |
          echo '::group::raw'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld/lib/curl_config.h | sort || true

      - name: 'build'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld ${MATRIX_OPTIONS} --parallel 4 --verbose
          else
            make -C bld V=1
          fi

      - name: 'curl info'
        run: find . -type f \( -name curl -o -name '*.dylib' -o -name '*.a' \) -exec file '{}' \;

      - name: 'build tests'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld ${MATRIX_OPTIONS} --parallel 4 --target testdeps --verbose
          else
            make -C bld V=1 -C tests
          fi

      - name: 'build examples'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld ${MATRIX_OPTIONS} --parallel 4 --target curl-examples --verbose
          else
            make -C bld examples V=1
          fi

  android:
    name: "Android ${{ matrix.platform }}, ${{ matrix.build == 'cmake' && 'CM' || 'AM' }} ${{ matrix.name }} arm64"
    runs-on: 'ubuntu-latest'
    timeout-minutes: 25
    env:
      MAKEFLAGS: -j 5
      MATRIX_BUILD: '${{ matrix.build }}'
    strategy:
      matrix:
        include:
          - { build: 'autotools', platform: '21', name: "!ssl !zstd",
              options: '--without-ssl --without-libpsl --without-zstd' }

          - { build: 'cmake'    , platform: '21', name: "!ssl !zstd",
              options: '-DCURL_ENABLE_SSL=OFF -DCURL_USE_LIBPSL=OFF -DCURL_ZSTD=OFF' }

          - { build: 'autotools', platform: '35', name: "!ssl !zstd",
              options: '--without-ssl --without-libpsl --without-zstd' }

          - { build: 'cmake'    , platform: '35', name: "!ssl !zstd",
              options: '-DCURL_ENABLE_SSL=OFF -DCURL_USE_LIBPSL=OFF -DCURL_ZSTD=OFF' }

      fail-fast: false
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'autoreconf'
        if: ${{ matrix.build == 'autotools' }}
        run: autoreconf -fi

      - name: 'configure'
        env:
          MATRIX_OPTIONS: '${{ matrix.options }}'
          MATRIX_PLATFORM: '${{ matrix.platform }}'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then  # https://developer.android.com/ndk/guides/cmake
            cmake -B bld -G Ninja \
              -DANDROID_ABI=arm64-v8a \
              -DANDROID_PLATFORM="android-${MATRIX_PLATFORM}" \
              -DCMAKE_TOOLCHAIN_FILE="${ANDROID_NDK_HOME}/build/cmake/android.toolchain.cmake" -DCMAKE_WARN_DEPRECATED=OFF \
              -DCMAKE_UNITY_BUILD=ON \
              -DCURL_WERROR=ON \
              ${MATRIX_OPTIONS}
          else
            TOOLCHAIN="${ANDROID_NDK_HOME}/toolchains/llvm/prebuilt/linux-x86_64"
            mkdir bld && cd bld && ../configure --disable-dependency-tracking --enable-unity --enable-warnings --enable-werror \
              CC="$TOOLCHAIN/bin/aarch64-linux-android${MATRIX_PLATFORM}-clang" \
              AR="$TOOLCHAIN/bin/llvm-ar" \
              RANLIB="$TOOLCHAIN/bin/llvm-ranlib" \
              --host="aarch64-linux-android${MATRIX_PLATFORM}" \
              ${MATRIX_OPTIONS}
          fi

      - name: 'configure log'
        if: ${{ !cancelled() }}
        run: cat bld/config.log bld/CMakeFiles/CMake*.yaml 2>/dev/null || true

      - name: 'curl_config.h'
        run: |
          echo '::group::raw'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld/lib/curl_config.h | sort || true

      - name: 'build'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose
          else
            make -C bld V=1
          fi

      - name: 'curl info'
        run: find . -type f \( -name curl -o -name '*.so' -o -name '*.a' \) -exec file '{}' \;

      - name: 'build tests'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --target testdeps
          else
            make -C bld -C tests
          fi

      - name: 'build examples'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --target curl-examples
          else
            make -C bld examples
          fi

  msdos:
    name: "MS-DOS, ${{ matrix.build == 'cmake' && 'CM' || 'AM' }} djgpp openssl i586"
    runs-on: 'ubuntu-latest'
    timeout-minutes: 5
    env:
      MAKEFLAGS: -j 5
      MATRIX_BUILD: '${{ matrix.build }}'
      TOOLCHAIN_VERSION: '3.4'
    strategy:
      matrix:
        build: [autotools, cmake]
      fail-fast: false
    steps:
      - name: 'install packages'
        run: |
          sudo rm -f /var/lib/man-db/auto-update
          sudo apt-get -o Dpkg::Use-Pty=0 install libfl2

      - name: 'cache compiler (djgpp)'
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf # v4
        id: cache-compiler
        with:
          path: ~/djgpp
          key: ${{ runner.os }}-djgpp-${{ env.TOOLCHAIN_VERSION }}-amd64

      - name: 'install compiler (djgpp)'
        if: ${{ steps.cache-compiler.outputs.cache-hit != 'true' }}
        run: |
          cd ~
          curl --disable --fail --silent --show-error --connect-timeout 15 --max-time 120 --retry 3 \
            --location "https://github.com/andrewwutw/build-djgpp/releases/download/v${TOOLCHAIN_VERSION}/djgpp-linux64-gcc1220.tar.bz2" | tar -xj
          cd djgpp
          for f in wat3211b.zip zlb13b.zip ssl102ub.zip; do
            curl --disable --fail --silent --show-error --connect-timeout 15 --max-time 60 --retry 3 \
              "https://www.delorie.com/pub/djgpp/current/v2tk/$f" --output bin.zip
            unzip -q bin.zip
            rm -f bin.zip
          done

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'configure'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake -B bld -G Ninja \
              -DCMAKE_SYSTEM_NAME=DOS \
              -DCMAKE_SYSTEM_PROCESSOR=x86 \
              -DCMAKE_C_COMPILER_TARGET=i586-pc-msdosdjgpp \
              -DCMAKE_C_COMPILER="$HOME/djgpp/bin/i586-pc-msdosdjgpp-gcc" \
              -DCMAKE_UNITY_BUILD=ON \
              -DCURL_WERROR=ON \
              -DCURL_USE_LIBPSL=OFF \
              -DOPENSSL_INCLUDE_DIR="$HOME/djgpp/include" \
              -DOPENSSL_SSL_LIBRARY="$HOME/djgpp/lib/libssl.a" \
              -DOPENSSL_CRYPTO_LIBRARY="$HOME/djgpp/lib/libcrypto.a" \
              -DZLIB_INCLUDE_DIR="$HOME/djgpp/include" \
              -DZLIB_LIBRARY="$HOME/djgpp/lib/libz.a" \
              -DWATT_ROOT="$HOME/djgpp/net/watt"
          else
            autoreconf -fi
            mkdir bld && cd bld && ../configure --disable-dependency-tracking --enable-unity --enable-warnings --enable-werror \
              CC="$HOME/djgpp/bin/i586-pc-msdosdjgpp-gcc" \
              AR="$HOME/djgpp/bin/i586-pc-msdosdjgpp-ar" \
              RANLIB="$HOME/djgpp/bin/i586-pc-msdosdjgpp-ranlib" \
              WATT_ROOT="$HOME/djgpp/net/watt" \
              --host=i586-pc-msdosdjgpp \
              --with-openssl="$HOME/djgpp" \
              --with-zlib="$HOME/djgpp" \
              --without-libpsl \
              --disable-shared
          fi

      - name: 'configure log'
        if: ${{ !cancelled() }}
        run: cat bld/config.log bld/CMakeFiles/CMake*.yaml 2>/dev/null || true

      - name: 'curl_config.h'
        run: |
          echo '::group::raw'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld/lib/curl_config.h | sort || true

      - name: 'build'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld
          else
            make -C bld
          fi

      - name: 'curl info'
        run: find . \( -name '*.exe' -o -name '*.a' \) -exec file '{}' \;

      - name: 'build tests'
        if: ${{ matrix.build == 'cmake' }}  # skip for autotools to save time
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --target testdeps
          else
            make -C bld -C tests
          fi

      - name: 'build examples'
        if: ${{ matrix.build == 'cmake' }}  # skip for autotools to save time
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --target curl-examples
          else
            make -C bld examples
          fi
