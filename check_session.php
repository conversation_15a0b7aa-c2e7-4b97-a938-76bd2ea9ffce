<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "=== VÉRIFICATION DES SESSIONS ===\n\n";
    
    // Vérifier la configuration des sessions
    echo "1. Configuration des sessions:\n";
    echo "Driver: " . config('session.driver') . "\n";
    echo "Lifetime: " . config('session.lifetime') . " minutes\n";
    echo "Encrypt: " . (config('session.encrypt') ? 'Oui' : 'Non') . "\n\n";
    
    // Vérifier les tables
    echo "2. Vérification des tables:\n";
    $tables = DB::select("SHOW TABLES");
    $tableNames = array_map(function($table) {
        return array_values((array)$table)[0];
    }, $tables);
    
    $sessionTables = ['sessions', 'cache', 'jobs'];
    foreach ($sessionTables as $table) {
        if (in_array($table, $tableNames)) {
            echo "✓ Table '$table' existe\n";
            
            if ($table === 'sessions') {
                $count = DB::table('sessions')->count();
                echo "  Sessions actives: $count\n";
            }
        } else {
            echo "✗ Table '$table' manquante\n";
        }
    }
    echo "\n";
    
    // Vérifier les routes Filament
    echo "3. Test des routes Filament:\n";
    $routes = collect(Route::getRoutes())->filter(function($route) {
        return str_contains($route->uri(), 'admin');
    });
    
    echo "Routes admin trouvées:\n";
    foreach ($routes as $route) {
        $methods = implode(', ', $route->methods());
        echo "- {$methods} {$route->uri()}\n";
    }
    
} catch (Exception $e) {
    echo "ERREUR: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
