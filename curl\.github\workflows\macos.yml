# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# SPDX-License-Identifier: curl

name: macOS

'on':
  push:
    branches:
      - master
      - '*/ci'
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'
  pull_request:
    branches:
      - master
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: {}

# Deprecated Apple APIs and the macos-version-min value required to avoid
# deprecation warnings with llvm/clang:
#
# - 10.7  Lion (2011)          - GSS
# - 10.9  Mavericks (2013)     - LDAP
#

env:
  CURL_CI: github
  MAKEFLAGS: -j 4
  LDFLAGS: -w  # suppress 'object file was built for newer macOS version than being linked' warnings

jobs:
  macos:
    name: "${{ matrix.build.generate && 'CM' || 'AM' }} ${{ matrix.compiler }} ${{ matrix.build.name }}"
    runs-on: 'macos-latest'
    timeout-minutes: 45
    env:
      DEVELOPER_DIR: "/Applications/Xcode${{ matrix.build.xcode && format('_{0}', matrix.build.xcode) || '' }}.app/Contents/Developer"
      CC: '${{ matrix.compiler }}'
      MATRIX_BUILD: ${{ matrix.build.generate && 'cmake' || 'autotools' }}
      MATRIX_COMPILER: '${{ matrix.compiler }}'
      MATRIX_INSTALL: '${{ matrix.build.install }}'
      MATRIX_MACOS_VERSION_MIN: '${{ matrix.build.macos-version-min }}'
    strategy:
      fail-fast: false
      matrix:
        compiler: [clang, llvm@15, gcc-12]
        build:
          # automake
          - name: '!ssl !debug brotli zstd'
            compiler: clang
            install: brotli zstd
            configure: --without-ssl --with-brotli --with-zstd
          - name: '!ssl !debug'
            compiler: gcc-12
            configure: --without-ssl
          - name: '!ssl'
            compiler: clang
            configure: --enable-debug --without-ssl
          - name: '!ssl libssh2 AppleIDN'
            compiler: clang
            configure: --enable-debug --with-libssh2=/opt/homebrew/opt/libssh2 --without-ssl --with-apple-idn
          - name: 'OpenSSL libssh c-ares'
            compiler: clang
            install: libssh
            configure: --enable-debug --with-libssh --with-openssl=/opt/homebrew/opt/openssl --enable-ares
          - name: 'OpenSSL libssh'
            compiler: llvm@15
            install: libssh libnghttp3
            configure: --enable-debug --with-libssh --with-openssl=/opt/homebrew/opt/openssl --with-openssl-quic
          - name: '!ssl c-ares'
            compiler: clang
            configure: --enable-debug --enable-ares --without-ssl
          - name: '!ssl HTTP-only'
            compiler: clang
            configure: >-
              --enable-debug
              --disable-alt-svc --disable-dict --disable-file --disable-ftp --disable-gopher --disable-imap
              --disable-ldap --disable-pop3 --without-librtmp --disable-rtsp
              --disable-shared --disable-smb --disable-smtp --disable-telnet --disable-tftp --disable-unix-sockets
              --without-brotli --without-gssapi --without-libidn2 --without-libpsl --without-librtmp
              --without-libssh2 --without-libssh --without-wolfssh
              --without-nghttp2 --disable-ntlm --without-ssl --without-zlib --without-zstd

            macos-version-min: '10.15'  # Catalina (2019)
          - name: 'LibreSSL +examples'
            compiler: clang
            install: libressl
            install_steps: pytest
            configure: --enable-debug --with-openssl=/opt/homebrew/opt/libressl
          - name: 'OpenSSL'
            compiler: clang
            install_steps: pytest
            configure: --enable-debug --with-openssl=/opt/homebrew/opt/openssl
          - name: 'OpenSSL event-based'
            compiler: clang
            configure: --enable-debug --with-openssl=/opt/homebrew/opt/openssl
            tflags: --test-event
          - name: 'quictls libssh2 !ldap 10.15'
            compiler: clang
            install: quictls
            configure: --enable-debug --disable-ldap --with-openssl=/opt/homebrew/opt/quictls LDFLAGS=-L/opt/homebrew/opt/quictls/lib
            macos-version-min: '10.15'
          # cmake
          - name: 'OpenSSL gsasl rtmp AppleIDN'
            install: gsasl rtmpdump
            generate: -DOPENSSL_ROOT_DIR=/opt/homebrew/opt/openssl -DCURL_USE_GSASL=ON -DUSE_LIBRTMP=ON -DUSE_APPLE_IDN=ON
          - name: 'MultiSSL AppleIDN clang-tidy +examples'
            install: llvm brotli zstd gnutls nettle mbedtls gsasl rtmpdump fish
            generate: -DCURL_USE_OPENSSL=ON -DOPENSSL_ROOT_DIR=/opt/homebrew/opt/openssl -DCURL_DEFAULT_SSL_BACKEND=openssl -DCURL_USE_GNUTLS=ON -DCURL_USE_MBEDTLS=ON -DENABLE_ARES=ON -DCURL_USE_GSASL=ON -DUSE_LIBRTMP=ON -DUSE_APPLE_IDN=ON -DUSE_SSLS_EXPORT=ON -DCURL_CLANG_TIDY=ON -DCLANG_TIDY=/opt/homebrew/opt/llvm/bin/clang-tidy -DCURL_COMPLETION_FISH=ON -DCURL_COMPLETION_ZSH=ON
            clang-tidy: true
            chkprefill: _chkprefill
          - name: 'quictls +static libssh +examples'
            install: quictls libssh
            generate: -DOPENSSL_ROOT_DIR=/opt/homebrew/opt/quictls -DBUILD_STATIC_LIBS=ON -DCURL_USE_LIBSSH2=OFF -DCURL_USE_LIBSSH=ON
          - name: 'LibreSSL !ldap heimdal c-ares +examples'
            install: libressl heimdal
            generate: -DOPENSSL_ROOT_DIR=/opt/homebrew/opt/libressl -DENABLE_ARES=ON -DCURL_USE_GSSAPI=ON -DGSS_ROOT_DIR=/opt/homebrew/opt/heimdal -DCURL_DISABLE_LDAP=ON
          - name: 'wolfSSL !ldap brotli zstd'
            install: brotli wolfssl zstd
            install_steps: pytest
            generate: -DCURL_USE_WOLFSSL=ON -DCURL_DISABLE_LDAP=ON -DUSE_ECH=ON
          - name: 'mbedTLS openldap brotli zstd'
            install: brotli mbedtls zstd openldap
            generate: -DCURL_USE_MBEDTLS=ON -DLDAP_INCLUDE_DIR=/opt/homebrew/opt/openldap/include -DLDAP_LIBRARY=/opt/homebrew/opt/openldap/lib/libldap.dylib -DLDAP_LBER_LIBRARY=/opt/homebrew/opt/openldap/lib/liblber.dylib
          - name: 'GnuTLS !ldap krb5'
            install: gnutls nettle krb5
            generate: -DCURL_USE_GNUTLS=ON -DCURL_USE_OPENSSL=OFF -DCURL_USE_GSSAPI=ON -DGSS_ROOT_DIR=/opt/homebrew/opt/krb5 -DCURL_DISABLE_LDAP=ON -DUSE_SSLS_EXPORT=ON
          - name: 'OpenSSL torture !FTP'
            generate: -DENABLE_DEBUG=ON -DBUILD_SHARED_LIBS=OFF -DENABLE_THREADED_RESOLVER=OFF -DOPENSSL_ROOT_DIR=/opt/homebrew/opt/openssl
            tflags: -t --shallow=25 !FTP
            torture: true
          - name: 'OpenSSL torture FTP'
            generate: -DENABLE_DEBUG=ON -DBUILD_SHARED_LIBS=OFF -DENABLE_THREADED_RESOLVER=OFF -DOPENSSL_ROOT_DIR=/opt/homebrew/opt/openssl
            tflags: -t --shallow=20 FTP
            torture: true
        exclude:
          - { compiler: llvm@15, build: { macos-version-min: '10.15' } }
          - { compiler: llvm@15, build: { torture: true } }
          - { compiler: gcc-12, build: { torture: true } }
          - { compiler: llvm@15, build: { clang-tidy: true } }
          - { compiler: gcc-12, build: { clang-tidy: true } }
          # opt out jobs from combinations that have the compiler set manually
          - { compiler: llvm@15, build: { compiler: 'clang' } }
          - { compiler: llvm@15, build: { compiler: 'gcc-12' } }
          - { compiler: gcc-12, build: { compiler: 'clang' } }
          - { compiler: gcc-12, build: { compiler: 'llvm@15' } }
          - { compiler: clang, build: { compiler: 'gcc-12' } }
          - { compiler: clang, build: { compiler: 'llvm@15' } }

    steps:
      - name: 'brew install'
        # Run this command with retries because of spurious failures seen
        # while running the tests, for example
        # https://github.com/curl/curl/runs/4095721123?check_suite_focus=true
        env:
          INSTALL_PACKAGES: >-
            ${{ matrix.build.generate && 'ninja' || 'automake libtool' }}
            ${{ !matrix.build.clang-tidy && 'nghttp2 stunnel' || '' }}
            ${{ contains(matrix.build.install_steps, 'pytest') && 'caddy httpd vsftpd' || '' }}

        run: |
          echo pkgconf libpsl libssh2 ${INSTALL_PACKAGES} ${MATRIX_INSTALL} | xargs -Ix -n1 echo brew '"x"' > /tmp/Brewfile
          # shellcheck disable=SC2181,SC2034
          while [[ $? == 0 ]]; do for i in 1 2 3; do if brew update && brew bundle install --file /tmp/Brewfile; then break 2; else echo Error: wait to try again; sleep 10; fi; done; false Too many retries; done

      - name: 'brew unlink openssl'
        if: ${{ contains(matrix.build.install, 'libressl') || contains(matrix.build.install, 'quictls') }}
        run: |
          if [ -d /opt/homebrew/include/openssl ]; then
            brew unlink openssl
          fi

      - name: 'toolchain versions'
        run: |
          [[ "${MATRIX_COMPILER}" = 'llvm'* ]] && CC="$(brew --prefix "${MATRIX_COMPILER}")/bin/clang"
          [[ "${MATRIX_COMPILER}" = 'gcc'* ]] && "${CC}" --print-sysroot
          command -v "${CC}"; "${CC}" --version || true
          xcodebuild -version || true
          xcrun --sdk macosx --show-sdk-path 2>/dev/null || true
          xcrun --sdk macosx --show-sdk-version || true
          ls -l /Library/Developer/CommandLineTools/SDKs || true
          echo '::group::macros predefined'; "${CC}" -dM -E - < /dev/null | sort || true; echo '::endgroup::'
          echo '::group::brew packages installed'; ls -l /opt/homebrew/opt; echo '::endgroup::'

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'autoreconf'
        if: ${{ matrix.build.configure }}
        run: autoreconf -fi

      - name: 'configure'
        env:
          MATRIX_CHKPREFILL: '${{ matrix.build.chkprefill }}'
          MATRIX_CONFIGURE: '${{ matrix.build.configure }}'
          MATRIX_GENERATE: '${{ matrix.build.generate }}'
          MATRIX_INSTALL_STEPS: '${{ matrix.build.install_steps }}'
        run: |
          if [[ "${MATRIX_COMPILER}" = 'gcc'* ]]; then
            sysroot="$("${CC}" --print-sysroot)"  # Must match the SDK gcc was built for
          else
            sysroot="$(xcrun --sdk macosx --show-sdk-path 2>/dev/null)"
          fi

          if [[ "${MATRIX_COMPILER}" = 'llvm'* ]]; then
            CC="$(brew --prefix "${MATRIX_COMPILER}")/bin/clang"
            CC+=" --sysroot=${sysroot}"
            CC+=" --target=$(uname -m)-apple-darwin"
          fi

          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            for _chkprefill in '' ${MATRIX_CHKPREFILL}; do
              options=''
              [ -n "${MATRIX_MACOS_VERSION_MIN}" ] && options+=" -DCMAKE_OSX_DEPLOYMENT_TARGET=${MATRIX_MACOS_VERSION_MIN}"
              [[ "${MATRIX_INSTALL_STEPS}" = *'pytest'* ]] && options+=' -DVSFTPD=NO'  # Skip ~20 tests that stretch run time by 7x on macOS
              [ "${_chkprefill}" = '_chkprefill' ] && options+=' -D_CURL_PREFILL=OFF'
              cmake -B "bld${_chkprefill}" -G Ninja -D_CURL_PREFILL=ON \
                -DCMAKE_INSTALL_PREFIX="$HOME"/curl-install \
                -DCMAKE_UNITY_BUILD=ON -DCURL_WERROR=ON \
                -DCMAKE_OSX_SYSROOT="${sysroot}" \
                -DCMAKE_C_COMPILER_TARGET="$(uname -m | sed 's/arm64/aarch64e/')-apple-darwin$(uname -r)" \
                ${MATRIX_GENERATE} ${options}
            done
            if [ -d bld_chkprefill ] && ! diff -u bld/lib/curl_config.h bld_chkprefill/lib/curl_config.h; then
              echo '::group::reference configure log'; cat bld_chkprefill/CMakeFiles/CMake*.yaml 2>/dev/null || true; echo '::endgroup::'
              false
            fi
          else
            export CFLAGS
            if [[ "${MATRIX_COMPILER}" = 'llvm'* ]]; then
              options+=" --target=$(uname -m)-apple-darwin"
            fi
            if [ "${MATRIX_COMPILER}" != 'clang' ]; then
              options+=" --with-sysroot=${sysroot}"
              CFLAGS+=" --sysroot=${sysroot}"
            fi
            [ -n "${MATRIX_MACOS_VERSION_MIN}" ] && CFLAGS+=" -mmacosx-version-min=${MATRIX_MACOS_VERSION_MIN}"
            [[ "${MATRIX_INSTALL_STEPS}" = *'pytest'* ]] && options+=' --with-test-vsftpd=no'  # Skip ~20 tests that stretch run time by 7x on macOS
            mkdir bld && cd bld && ../configure --enable-unity --enable-warnings --enable-werror \
              --disable-dependency-tracking \
              --with-libpsl=/opt/homebrew/opt/libpsl \
              ${MATRIX_CONFIGURE} ${options}
          fi

      - name: 'configure log'
        if: ${{ !cancelled() }}
        run: cat bld/config.log bld/CMakeFiles/CMakeConfigureLog.yaml 2>/dev/null || true

      - name: 'curl_config.h'
        run: |
          echo '::group::raw'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld/lib/curl_config.h | sort || true

      - name: 'test configs'
        run: grep -H -v '^#' bld/tests/config bld/tests/http/config.ini || true

      - name: 'build'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose
          else
            make -C bld V=1
          fi

      - name: 'curl version'
        run: bld/src/curl --disable --version

      - name: 'cmake install'
        if: ${{ matrix.build.generate }}
        run: cmake --install bld --strip

      - name: 'build tests'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose --target testdeps
          else
            make -C bld V=1 -C tests
          fi

      - name: 'install test prereqs'
        if: ${{ !matrix.build.clang-tidy }}
        run: |
          python3 -m venv ~/venv
          source ~/venv/bin/activate
          python3 -m pip install -r tests/requirements.txt

      - name: 'run tests'
        if: ${{ !matrix.build.clang-tidy }}
        timeout-minutes: ${{ matrix.build.torture && 20 || 10 }}
        env:
          TEST_TARGET: ${{ matrix.build.torture && 'test-torture' || 'test-ci' }}
          TFLAGS: '${{ matrix.build.tflags }}'
        run: |
          TFLAGS="-j20 ${TFLAGS}"
          source ~/venv/bin/activate
          rm -f ~/.curlrc
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose --target "${TEST_TARGET}"
          else
            make -C bld V=1 "${TEST_TARGET}"
          fi

      - name: 'install pytest prereqs'
        if: ${{ !matrix.build.clang-tidy && contains(matrix.build.install_steps, 'pytest') }}
        run: |
          source ~/venv/bin/activate
          python3 -m pip install -r tests/http/requirements.txt

      - name: 'run pytest'
        if: ${{ !matrix.build.clang-tidy && contains(matrix.build.install_steps, 'pytest') }}
        env:
          PYTEST_ADDOPTS: '--color=yes'
          PYTEST_XDIST_AUTO_NUM_WORKERS: 4
        run: |
          source ~/venv/bin/activate
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose --target curl-pytest-ci
          else
            make -C bld V=1 pytest-ci
          fi

      - name: 'build examples'
        if: ${{ contains(matrix.build.name, '+examples') }}
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose --target curl-examples
          else
            make -C bld examples V=1
          fi

  combinations:  # Test buildability with host OS, Xcode / SDK, compiler, target-OS, built tool, combinations
    name: "${{ matrix.build == 'cmake' && 'CM' || 'AM' }} ${{ matrix.compiler }} ${{ matrix.image }} ${{ matrix.xcode }} ${{ matrix.config }}"
    runs-on: ${{ matrix.image }}
    timeout-minutes: 10
    env:
      DEVELOPER_DIR: "/Applications/Xcode${{ matrix.xcode && format('_{0}', matrix.xcode) || '' }}.app/Contents/Developer"
      CC: '${{ matrix.compiler }}'
      MATRIX_BUILD: '${{ matrix.build }}'
      MATRIX_COMPILER: '${{ matrix.compiler }}'
      MATRIX_IMAGE: '${{ matrix.image }}'
      MATRIX_MACOS_VERSION_MIN: '${{ matrix.macos-version-min }}'
    strategy:
      fail-fast: false
      matrix:
        compiler: [gcc-12, gcc-13, gcc-14, llvm@15, llvm@18, clang]
        # Xcode support matrix as of 2024-07, with default macOS SDK versions and OS names, years:
        # * = default Xcode on the runner.
        # macos-13: 14.1, 14.2, 14.3.1, 15.0.1, 15.1,*15.2
        # macos-14:                     15.0.1, 15.1, 15.2, 15.3,*15.4
        # macos-15:                                                    *16.0, 16.1
        # macOSSDK: 13.0, 13.1, 13.3,   14.0,   14.2, 14.2, 14.4, 14.5, 15.0, 15.1
        #           Ventura (2022)      Sonoma (2023)                   Sequoia (2024)
        # https://github.com/actions/runner-images/tree/main/images/macos
        # https://en.wikipedia.org/wiki/MacOS_version_history
        # TODO when dropping macos-13: replace '$(brew --prefix ...' with /opt/homebrew
        image: [macos-13, macos-14, macos-15]
        # Can skip these to reduce jobs:
        #   15.1 has the same default macOS SDK as 15.2 and identical test results.
        #   14.1, 15.4 not revealing new fallouts.
        #xcode: ['14.1', '14.2', '14.3.1', '15.0.1', '15.1', '15.2', '15.3', '15.4', '16.0', '16.1']  # all Xcode
        #xcode: ['14.1', '14.2', '14.3.1', '15.0.1'        , '15.2', '15.3', '15.4', '16.0', '16.1']  # all SDK
        #xcode: [        '14.2', '14.3.1', '15.0.1'        , '15.2', '15.3'        , '16.0'        ]  # coverage
        xcode: ['']  # default Xcodes
        macos-version-min: ['']
        build: [autotools, cmake]
        exclude:
          # Combinations not covered by runner images:
          - { image: macos-13, xcode: '15.3'   }
          - { image: macos-13, xcode: '15.4'   }
          - { image: macos-13, xcode: '16.0'   }
          - { image: macos-13, xcode: '16.1'   }
          - { image: macos-14, xcode: '14.1'   }
          - { image: macos-14, xcode: '14.2'   }
          - { image: macos-14, xcode: '14.3.1' }
          - { image: macos-14, xcode: '16.0'   }
          - { image: macos-14, xcode: '16.1'   }
          - { image: macos-15, xcode: '14.1'   }
          - { image: macos-15, xcode: '14.2'   }
          - { image: macos-15, xcode: '14.3.1' }
          - { image: macos-15, xcode: '15.0.1' }
          - { image: macos-15, xcode: '15.1'   }
          - { image: macos-15, xcode: '15.2'   }
          - { image: macos-15, xcode: '15.3'   }
          - { image: macos-15, xcode: '15.4'   }
          - { image: macos-13, compiler: 'llvm@18' }
          - { image: macos-14, compiler: 'llvm@18' }
          - { image: macos-15, compiler: 'llvm@15' }
          # Reduce build combinations, by dropping less interesting ones
          - { compiler: gcc-13, build: cmake }
          - { compiler: gcc-14, build: autotools }
    steps:
      - name: 'install autotools'
        if: ${{ matrix.build == 'autotools' }}
        run: |
          # shellcheck disable=SC2181,SC2034
          while [[ $? == 0 ]]; do for i in 1 2 3; do if brew update && brew install automake libtool; then break 2; else echo Error: wait to try again; sleep 10; fi; done; false Too many retries; done

      - name: 'toolchain versions'
        run: |
          [[ "${MATRIX_COMPILER}" = 'llvm'* ]] && CC="$(brew --prefix "${MATRIX_COMPILER}")/bin/clang"
          [[ "${MATRIX_COMPILER}" = 'gcc'* ]] && "${CC}" --print-sysroot
          command -v "${CC}"; "${CC}" --version || true
          xcodebuild -version || true
          xcrun --sdk macosx --show-sdk-path 2>/dev/null || true
          xcrun --sdk macosx --show-sdk-version || true
          ls -l /Library/Developer/CommandLineTools/SDKs || true
          echo '::group::macros predefined'; "${CC}" -dM -E - < /dev/null | sort || true; echo '::endgroup::'
          echo '::group::brew packages preinstalled'; ls -l "$(brew --prefix)/opt"; echo '::endgroup::'

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'autoreconf'
        if: ${{ matrix.build == 'autotools' }}
        run: autoreconf -fi

      - name: 'configure / ${{ matrix.build }}'
        run: |
          if [ "${MATRIX_COMPILER}" = 'gcc-13' ] && [ "${MATRIX_IMAGE}" = 'macos-15' ] ; then
            # Ref: https://github.com/Homebrew/homebrew-core/issues/194778#issuecomment-2793243409
            /opt/homebrew/opt/gcc@13/libexec/gcc/aarch64-apple-darwin24/13/install-tools/mkheaders
          fi

          if [[ "${MATRIX_COMPILER}" = 'gcc'* ]]; then
            sysroot="$("${CC}" --print-sysroot)"  # Must match the SDK gcc was built for
          else
            sysroot="$(xcrun --sdk macosx --show-sdk-path 2>/dev/null)"
          fi

          if [[ "${MATRIX_COMPILER}" = 'llvm'* ]]; then
            CC="$(brew --prefix "${MATRIX_COMPILER}")/bin/clang"
            CC+=" --sysroot=${sysroot}"
            CC+=" --target=$(uname -m)-apple-darwin"
          fi

          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            [ -n "${MATRIX_MACOS_VERSION_MIN}" ] && options+=" -DCMAKE_OSX_DEPLOYMENT_TARGET=${MATRIX_MACOS_VERSION_MIN}"
            # would pick up nghttp2, libidn2, and libssh2
            cmake -B bld -G Ninja -D_CURL_PREFILL=ON \
              -DCMAKE_UNITY_BUILD=ON -DCURL_WERROR=ON \
              -DCMAKE_OSX_SYSROOT="${sysroot}" \
              -DCMAKE_C_COMPILER_TARGET="$(uname -m | sed 's/arm64/aarch64e/')-apple-darwin$(uname -r)" \
              -DCMAKE_IGNORE_PREFIX_PATH="$(brew --prefix)" \
              -DBUILD_LIBCURL_DOCS=OFF -DBUILD_MISC_DOCS=OFF -DENABLE_CURL_MANUAL=OFF \
              -DCURL_USE_OPENSSL=ON \
              -DUSE_NGHTTP2=OFF -DUSE_LIBIDN2=OFF \
              -DCURL_USE_LIBPSL=OFF -DCURL_USE_LIBSSH2=OFF \
              ${options}
          else
            export CFLAGS
            if [[ "${MATRIX_COMPILER}" = 'llvm'* ]]; then
              options+=" --target=$(uname -m)-apple-darwin"
            fi
            if [ "${MATRIX_COMPILER}" != 'clang' ]; then
              options+=" --with-sysroot=${sysroot}"
              CFLAGS+=" --sysroot=${sysroot}"
            fi
            [ -n "${MATRIX_MACOS_VERSION_MIN}" ] && CFLAGS+=" -mmacosx-version-min=${MATRIX_MACOS_VERSION_MIN}"
            # would pick up nghttp2, libidn2, but libssh2 is disabled by default
            mkdir bld && cd bld && ../configure --enable-unity --enable-warnings --enable-werror \
              --disable-dependency-tracking \
              --disable-docs --disable-manual \
              --with-openssl="$(brew --prefix openssl)" \
              --without-nghttp2 --without-libidn2 \
              --without-libpsl \
              ${options}
          fi

      - name: 'configure log'
        if: ${{ !cancelled() }}
        run: cat bld/config.log bld/CMakeFiles/CMakeConfigureLog.yaml 2>/dev/null || true

      - name: 'curl_config.h'
        run: |
          echo '::group::raw'; cat bld/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld/lib/curl_config.h | sort || true

      - name: 'build / ${{ matrix.build }}'
        run: |
          if [ "${MATRIX_BUILD}" = 'cmake' ]; then
            cmake --build bld --verbose
          else
            make -C bld V=1
          fi

      - name: 'curl version'
        run: bld/src/curl --disable --version
