# Copyright (C) <PERSON>
#
# SPDX-License-Identifier: curl
---
name: curl-for-win

'on':
  push:
    branches:
      - master
      - '*/ci'
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'
  pull_request:
    branches:
      - master
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: {}

env:
  CW_NOGET: 'curl trurl'
  CW_MAP: '0'
  CW_JOBS: '5'
  CW_NOPKG: '1'

jobs:
  linux-glibc-gcc:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
          path: 'curl'
          fetch-depth: 8
      - name: 'build'
        run: |
          git clone --depth 1 https://github.com/curl/curl-for-win
          mv curl-for-win/* .
          export CW_CONFIG='-main-werror-linux-a64-x64-gcc'
          export CW_REVISION="${GITHUB_SHA}"
          DOCKER_IMAGE='debian:bookworm-slim'
          export CW_CCSUFFIX='-15'
          export CW_GCCSUFFIX='-12'
          sudo podman image trust set --type reject default
          sudo podman image trust set --type accept docker.io/library
          time podman pull "${DOCKER_IMAGE}"
          podman images --digests
          time podman run --volume "$(pwd):$(pwd)" --workdir "$(pwd)" \
            --env-file <(env | grep -a -E \
              '^(CW_|GITHUB_)') \
            "${DOCKER_IMAGE}" \
            sh -c ./_ci-linux-debian.sh

  linux-musl-llvm:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
          path: 'curl'
          fetch-depth: 8
      - name: 'build'
        run: |
          git clone --depth 1 https://github.com/curl/curl-for-win
          mv curl-for-win/* .
          export CW_CONFIG='-main-werror-linux-musl-r64-x64'
          export CW_REVISION="${GITHUB_SHA}"
          . ./_versions.sh
          sudo podman image trust set --type reject default
          sudo podman image trust set --type accept docker.io/library
          time podman pull "${DOCKER_IMAGE}"
          podman images --digests
          time podman run --volume "$(pwd):$(pwd)" --workdir "$(pwd)" \
            --env-file <(env | grep -a -E \
              '^(CW_|GITHUB_)') \
            "${DOCKER_IMAGE}" \
            sh -c ./_ci-linux-debian.sh

  mac-clang:
    runs-on: macos-latest
    timeout-minutes: 30
    env:
      CW_JOBS: '4'
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
          path: 'curl'
          fetch-depth: 8
      - name: 'build'
        run: |
          git clone --depth 1 https://github.com/curl/curl-for-win
          mv curl-for-win/* .
          export CW_CONFIG='-main-werror-mac-x64'
          export CW_REVISION="${GITHUB_SHA}"
          sh -c ./_ci-mac-homebrew.sh

  win-llvm:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
          path: 'curl'
          fetch-depth: 8
      - name: 'build'
        run: |
          git clone --depth 1 https://github.com/curl/curl-for-win
          mv curl-for-win/* .
          export CW_CONFIG='-main-werror-win-x64'
          export CW_REVISION="${GITHUB_SHA}"
          . ./_versions.sh
          sudo podman image trust set --type reject default
          sudo podman image trust set --type accept docker.io/library
          time podman pull "${DOCKER_IMAGE}"
          podman images --digests
          time podman run --volume "$(pwd):$(pwd)" --workdir "$(pwd)" \
            --env-file <(env | grep -a -E \
              '^(CW_|GITHUB_)') \
            "${DOCKER_IMAGE}" \
            sh -c ./_ci-linux-debian.sh

  win-gcc-libssh-zlibold-x86:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false
          path: 'curl'
          fetch-depth: 8
      - name: 'build'
        run: |
          git clone --depth 1 https://github.com/curl/curl-for-win
          mv curl-for-win/* .
          export CW_CONFIG='-main-werror-win-x86-gcc-libssh1-zlibng'
          export CW_REVISION="${GITHUB_SHA}"
          . ./_versions.sh
          sudo podman image trust set --type reject default
          sudo podman image trust set --type accept docker.io/library
          time podman pull "${DOCKER_IMAGE}"
          podman images --digests
          time podman run --volume "$(pwd):$(pwd)" --workdir "$(pwd)" \
            --env-file <(env | grep -a -E \
              '^(CW_|GITHUB_)') \
            "${DOCKER_IMAGE}" \
            sh -c ./_ci-linux-debian.sh
