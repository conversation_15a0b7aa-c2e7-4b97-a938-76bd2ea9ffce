<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    public function run()
    {
        // Créer les rôles
        $admin = Role::firstOrCreate(['name' => 'admin']);
        $superadmin = Role::firstOrCreate(['name' => 'superadmin']);
        $utilisateur = Role::firstOrCreate(['name' => 'utilisateur']);

        // Créer des permissions de base (exemple)
        $permissions = [
            'view users',
            'create users',
            'edit users',
            'delete users',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Attribuer toutes les permissions à admin et superadmin
        $admin->syncPermissions($permissions);
        $superadmin->syncPermissions($permissions);
    }
}