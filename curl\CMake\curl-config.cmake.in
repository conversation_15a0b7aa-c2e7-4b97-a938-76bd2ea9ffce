#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
# SPDX-License-Identifier: curl
#
###########################################################################
@PACKAGE_INIT@

include(CMakeFindDependencyMacro)
if("@USE_OPENSSL@")
  if("@OPENSSL_VERSION_MAJOR@")
    find_dependency(OpenSSL "@OPENSSL_VERSION_MAJOR@")
  else()
    find_dependency(OpenSSL)
  endif()
endif()
if("@HAVE_LIBZ@")
  find_dependency(ZLIB "@ZLIB_VERSION_MAJOR@")
endif()

include("${CMAKE_CURRENT_LIST_DIR}/@TARGETS_EXPORT_NAME@.cmake")

# Alias for either shared or static library
if(NOT TARGET @PROJECT_NAME@::@LIB_NAME@)
  if(CMAKE_VERSION VERSION_GREATER_EQUAL 3.11 AND CMAKE_VERSION VERSION_LESS 3.18)
    set_target_properties(@PROJECT_NAME@::@LIB_SELECTED@ PROPERTIES IMPORTED_GLOBAL TRUE)
  endif()
  add_library(@PROJECT_NAME@::@LIB_NAME@ ALIAS @PROJECT_NAME@::@LIB_SELECTED@)
endif()

# For compatibility with CMake's FindCURL.cmake
set(CURL_VERSION_STRING "@CURLVERSION@")
set(CURL_LIBRARIES @PROJECT_NAME@::@LIB_NAME@)
set_and_check(CURL_INCLUDE_DIRS "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")

set(CURL_SUPPORTED_PROTOCOLS "@CURL_SUPPORTED_PROTOCOLS_LIST@")
set(CURL_SUPPORTED_FEATURES "@CURL_SUPPORTED_FEATURES_LIST@")

foreach(_item IN LISTS CURL_SUPPORTED_PROTOCOLS CURL_SUPPORTED_FEATURES)
  set(CURL_SUPPORTS_${_item} TRUE)
endforeach()

set(_missing_req "")
foreach(_item IN LISTS CURL_FIND_COMPONENTS)
  if(CURL_SUPPORTS_${_item})
    set(CURL_${_item}_FOUND TRUE)
  elseif(CURL_FIND_REQUIRED_${_item})
    list(APPEND _missing_req ${_item})
  endif()
endforeach()

if(_missing_req)
  string(REPLACE ";" " " _missing_req "${_missing_req}")
  if(CURL_FIND_REQUIRED)
    message(FATAL_ERROR "CURL: missing required components: ${_missing_req}")
  endif()
  unset(_missing_req)
endif()

check_required_components("@PROJECT_NAME@")
