# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# SPDX-License-Identifier: curl

name: dist

'on':
  push:
    branches:
      - master
      - '*/ci'
  pull_request:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

permissions: {}

env:
  MAKEFLAGS: -j 5

jobs:
  maketgz-and-verify-in-tree:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'remove preinstalled curl libcurl4{-doc}'
        run: sudo apt-get -o Dpkg::Use-Pty=0 purge curl libcurl4 libcurl4-doc

      - name: 'autoreconf'
        run: autoreconf -fi

      - name: 'configure'
        run: ./configure --without-ssl --without-libpsl

      - name: 'make'
        run: make V=1

      - name: 'maketgz'
        run: SOURCE_DATE_EPOCH=********** ./scripts/maketgz 99.98.97

      - uses: actions/upload-artifact@4cec3d8aa04e39d1a68397de0c4cd6fb9dce8ec1 # v4
        with:
          name: 'release-tgz'
          path: 'curl-99.98.97.tar.gz'
          retention-days: 1

      - name: 'verify in-tree configure build including install'
        run: |
          echo "::stop-commands::$(uuidgen)"
          tar xvf curl-99.98.97.tar.gz
          pushd curl-99.98.97
          ./configure --prefix="$HOME"/temp --without-ssl --without-libpsl
          make
          make test-ci
          make install
          popd
          # basic check of the installed files
          bash scripts/installcheck.sh "$HOME"/temp
          rm -rf curl-99.98.97

  verify-out-of-tree-docs:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: maketgz-and-verify-in-tree
    steps:
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4
        with:
          name: 'release-tgz'

      - name: 'verify out-of-tree configure build including docs'
        run: |
          echo "::stop-commands::$(uuidgen)"
          tar xvf curl-99.98.97.tar.gz
          touch curl-99.98.97/docs/{cmdline-opts,libcurl}/Makefile.inc
          mkdir build
          pushd build
          ../curl-99.98.97/configure --without-ssl --without-libpsl
          make
          make test-ci
          popd
          rm -rf build
          rm -rf curl-99.98.97

  verify-out-of-tree-autotools-debug:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: maketgz-and-verify-in-tree
    steps:
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4
        with:
          name: 'release-tgz'

      - name: 'verify out-of-tree autotools debug build'
        run: |
          echo "::stop-commands::$(uuidgen)"
          tar xvf curl-99.98.97.tar.gz
          pushd curl-99.98.97
          mkdir build
          pushd build
          ../configure --without-ssl --enable-debug --prefix="$PWD"/curl-install --without-libpsl
          make
          make test-ci
          make install
          popd
          scripts/checksrc-all.sh

  verify-out-of-tree-cmake:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: maketgz-and-verify-in-tree
    steps:
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4
        with:
          name: 'release-tgz'

      - name: 'verify out-of-tree cmake build'
        run: |
          echo "::stop-commands::$(uuidgen)"
          tar xvf curl-99.98.97.tar.gz
          pushd curl-99.98.97
          cmake -B build -DCURL_WERROR=ON -DCURL_USE_LIBPSL=OFF
          make -C build

  missing-files:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: maketgz-and-verify-in-tree
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4
        with:
          name: 'release-tgz'

      - name: 'detect files missing from release tarball'
        run: .github/scripts/distfiles.sh curl-99.98.97.tar.gz

  reproducible-releases:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'remove preinstalled curl libcurl4{-doc}'
        run: sudo apt-get -o Dpkg::Use-Pty=0 purge curl libcurl4 libcurl4-doc

      - name: 'generate release tarballs'
        run: ./scripts/dmaketgz 9.10.11

      - name: 'verify release tarballs'
        run: |
          mkdir _verify
          mv curl-9.10.11.tar.gz _verify
          cd _verify
          ../scripts/verify-release curl-9.10.11.tar.gz

  cmake-integration:
    name: 'cmake-integration-on-${{ matrix.image }}'
    runs-on: ${{ matrix.image }}
    timeout-minutes: 10
    defaults:
      run:
        shell: ${{ contains(matrix.image, 'windows') && 'msys2 {0}' || 'bash' }}
    env:
      CC: ${{ !contains(matrix.image, 'windows') && 'clang' || '' }}
      MATRIX_IMAGE: '${{ matrix.image }}'
      TESTOPTS: ${{ contains(matrix.image, 'macos') && '-D_CURL_PREFILL=ON' || '' }} ${{ contains(matrix.image, 'windows') && '-DCMAKE_UNITY_BUILD_BATCH_SIZE=30' || '' }}
      OLD_CMAKE_VERSION: 3.11.4
    strategy:
      fail-fast: false
      matrix:
        image: [ubuntu-latest, macos-latest, windows-latest]
    steps:
      - uses: msys2/setup-msys2@40677d36a502eb2cf0fb808cc9dec31bf6152638 # v2
        if: ${{ contains(matrix.image, 'windows') }}
        with:
          msystem: mingw64
          release: false
          update: false
          cache: false
          path-type: inherit
          install: >-
            mingw-w64-x86_64-zlib mingw-w64-x86_64-zstd mingw-w64-x86_64-libpsl mingw-w64-x86_64-libssh2 mingw-w64-x86_64-nghttp2 mingw-w64-x86_64-openssl

      - name: 'install prereqs'
        run: |
          if [[ "${MATRIX_IMAGE}" = *'windows'* ]]; then
            cd ~
            curl --disable --fail --silent --show-error --connect-timeout 15 --max-time 60 --retry 3 --retry-connrefused \
              --location "https://github.com/Kitware/CMake/releases/download/v${OLD_CMAKE_VERSION}/cmake-${OLD_CMAKE_VERSION}-win64-x64.zip" --output bin.zip
            unzip -q bin.zip
            rm -f bin.zip
            printf '%s' ~/cmake-"${OLD_CMAKE_VERSION}"-win64-x64/bin/cmake.exe > ~/old-cmake-path.txt
          elif [[ "${MATRIX_IMAGE}" = *'ubuntu'* ]]; then
            sudo rm -f /var/lib/man-db/auto-update
            sudo apt-get -o Dpkg::Use-Pty=0 install libpsl-dev libssl-dev
            cd ~
            curl --disable --fail --silent --show-error --connect-timeout 15 --max-time 60 --retry 3 --retry-connrefused \
              --location "https://github.com/Kitware/CMake/releases/download/v${OLD_CMAKE_VERSION}/cmake-${OLD_CMAKE_VERSION}-Linux-x86_64.tar.gz" | tar -xz
            printf '%s' ~/cmake-"${OLD_CMAKE_VERSION}"-Linux-x86_64/bin/cmake > ~/old-cmake-path.txt
          else
            brew install libpsl openssl
            cd ~
            curl --disable --fail --silent --show-error --connect-timeout 15 --max-time 60 --retry 3 --retry-connrefused \
              --location "https://github.com/Kitware/CMake/releases/download/v${OLD_CMAKE_VERSION}/cmake-${OLD_CMAKE_VERSION}-Darwin-x86_64.tar.gz" | tar -xz
            printf '%s' ~/cmake-"${OLD_CMAKE_VERSION}"-Darwin-x86_64/CMake.app/Contents/bin/cmake > ~/old-cmake-path.txt
          fi

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'via FetchContent'
        run: ./tests/cmake/test.sh FetchContent ${TESTOPTS} -DCURL_USE_OPENSSL=ON
      - name: 'via add_subdirectory'
        run: ./tests/cmake/test.sh add_subdirectory ${TESTOPTS} -DCURL_USE_OPENSSL=ON
      - name: 'via find_package'
        run: ./tests/cmake/test.sh find_package ${TESTOPTS} -DCURL_USE_OPENSSL=ON

      - name: 'via add_subdirectory OpenSSL (old cmake)'
        run: |
          export TEST_CMAKE_CONSUMER; TEST_CMAKE_CONSUMER="$(cat ~/old-cmake-path.txt)"
          if [[ "${MATRIX_IMAGE}" = *'macos'* ]]; then
            export CFLAGS='-arch arm64'
            export TEST_CMAKE_FLAGS='-DCURL_USE_LIBPSL=OFF'  # auto-detection does not work with old-cmake
          fi
          if [[ "${MATRIX_IMAGE}" = *'windows'* ]]; then
            export TEST_CMAKE_GENERATOR='MSYS Makefiles'
            export TEST_CMAKE_FLAGS='-DCMAKE_C_COMPILER=x86_64-w64-mingw32-gcc -DOPENSSL_ROOT_DIR=C:/msys64/mingw64'
          fi
          ./tests/cmake/test.sh add_subdirectory ${TESTOPTS} -DCURL_USE_OPENSSL=ON

      - name: 'via find_package OpenSSL (old cmake)'
        run: |
          export TEST_CMAKE_CONSUMER; TEST_CMAKE_CONSUMER="$(cat ~/old-cmake-path.txt)"
          if [[ "${MATRIX_IMAGE}" = *'macos'* ]]; then
            export CFLAGS='-arch arm64'
            export TEST_CMAKE_FLAGS='-DCURL_USE_LIBPSL=OFF'  # auto-detection does not work with old-cmake
          fi
          if [[ "${MATRIX_IMAGE}" = *'windows'* ]]; then
            export TEST_CMAKE_GENERATOR='MSYS Makefiles'
            export TEST_CMAKE_FLAGS='-DCMAKE_C_COMPILER=x86_64-w64-mingw32-gcc -DOPENSSL_ROOT_DIR=C:/msys64/mingw64'
          fi
          ./tests/cmake/test.sh find_package ${TESTOPTS} -DCURL_USE_OPENSSL=ON
