<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "=== DIAGNOSTIC DES RÔLES ===\n\n";
    
    // Test de connexion DB
    echo "1. Test de connexion à la base de données:\n";
    $pdo = DB::connection()->getPdo();
    echo "✓ Connexion réussie à: " . config('database.default') . "\n";
    echo "✓ Base de données: " . config('database.connections.mysql.database') . "\n\n";
    
    // Vérifier les tables
    echo "2. Vérification des tables:\n";
    $tables = DB::select("SHOW TABLES");
    $tableNames = array_map(function($table) {
        return array_values((array)$table)[0];
    }, $tables);
    
    $requiredTables = ['users', 'roles', 'permissions', 'model_has_roles', 'role_has_permissions'];
    foreach ($requiredTables as $table) {
        if (in_array($table, $tableNames)) {
            echo "✓ Table '$table' existe\n";
        } else {
            echo "✗ Table '$table' manquante\n";
        }
    }
    echo "\n";
    
    // Compter les enregistrements
    echo "3. Comptage des enregistrements:\n";
    echo "Users: " . App\Models\User::count() . "\n";
    echo "Roles: " . Spatie\Permission\Models\Role::count() . "\n";
    echo "Permissions: " . Spatie\Permission\Models\Permission::count() . "\n\n";
    
    // Lister les rôles
    echo "4. Rôles existants:\n";
    $roles = Spatie\Permission\Models\Role::all();
    if ($roles->count() > 0) {
        foreach ($roles as $role) {
            echo "- {$role->name} (ID: {$role->id})\n";
        }
    } else {
        echo "Aucun rôle trouvé\n";
    }
    echo "\n";
    
    // Vérifier les utilisateurs et leurs rôles
    echo "5. Utilisateurs et leurs rôles:\n";
    $users = App\Models\User::with('roles')->get();
    if ($users->count() > 0) {
        foreach ($users as $user) {
            $userRoles = $user->roles->pluck('name')->implode(', ');
            echo "- {$user->email}: " . ($userRoles ?: 'Aucun rôle') . "\n";
        }
    } else {
        echo "Aucun utilisateur trouvé\n";
    }
    echo "\n";
    
    // Test de la relation
    echo "6. Test de la relation User->roles:\n";
    $firstUser = App\Models\User::first();
    if ($firstUser) {
        echo "Premier utilisateur: {$firstUser->email}\n";
        echo "Méthode getRoleNames(): " . $firstUser->getRoleNames()->implode(', ') . "\n";
        echo "Relation roles: " . $firstUser->roles->pluck('name')->implode(', ') . "\n";
        echo "HasRole('admin'): " . ($firstUser->hasRole('admin') ? 'Oui' : 'Non') . "\n";
    } else {
        echo "Aucun utilisateur pour tester\n";
    }
    
} catch (Exception $e) {
    echo "ERREUR: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
