<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "=== CORRECTION DU PROBLÈME DE CONNEXION ===\n\n";
    
    // 1. Vérifier et créer la table sessions si nécessaire
    echo "1. Vérification de la table sessions:\n";
    
    if (!Schema::hasTable('sessions')) {
        echo "Table sessions manquante, création...\n";
        
        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
        
        echo "✓ Table sessions créée\n";
    } else {
        echo "✓ Table sessions existe déjà\n";
    }
    
    // 2. Nettoyer le cache
    echo "\n2. Nettoyage du cache:\n";
    Artisan::call('config:clear');
    echo "✓ Config cache cleared\n";
    
    Artisan::call('route:clear');
    echo "✓ Route cache cleared\n";
    
    Artisan::call('view:clear');
    echo "✓ View cache cleared\n";
    
    // 3. Vérifier la configuration APP_KEY
    echo "\n3. Vérification de APP_KEY:\n";
    $appKey = config('app.key');
    if (empty($appKey)) {
        echo "✗ APP_KEY manquante\n";
        echo "Exécutez: php artisan key:generate\n";
    } else {
        echo "✓ APP_KEY configurée\n";
    }
    
    // 4. Tester la création d'une session
    echo "\n4. Test de session:\n";
    try {
        session()->start();
        session()->put('test', 'value');
        $value = session()->get('test');
        if ($value === 'value') {
            echo "✓ Sessions fonctionnent\n";
        } else {
            echo "✗ Problème avec les sessions\n";
        }
        session()->forget('test');
    } catch (Exception $e) {
        echo "✗ Erreur de session: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== CORRECTION TERMINÉE ===\n";
    echo "Essayez maintenant de vous connecter à: http://localhost/ihsaa/public/admin\n";
    
} catch (Exception $e) {
    echo "ERREUR: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
