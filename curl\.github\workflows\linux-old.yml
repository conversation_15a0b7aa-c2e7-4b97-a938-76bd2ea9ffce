# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# SPDX-License-Identifier: curl
#
# Compile on an old version of Linux that has barely the minimal build
# requirements for CMake. This tests that curl is still usable on really
# outdated systems.
#
# Debian stretch is chosen as it closely matches some of the oldest major
# versions we support (especially cmake); see docs/INTERNALS.md and it
# is still supported (as of this writing).
# stretch has ELTS support from Freexian until 2027-06-30
# For ELTS info see https://www.freexian.com/lts/extended/docs/how-to-use-extended-lts/
# The Debian key will expire 2025-05-20, after which package signature
# verification may need to be disabled.
# httrack is one of the smallest downloaders, needed to bootstrap ELTS,
# and won't conflict with the curl we're building.

name: Old Linux

'on':
  push:
    branches:
      - master
      - '*/ci'
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'
  pull_request:
    branches:
      - master
    paths-ignore:
      - '**/*.md'
      - '.circleci/**'
      - 'appveyor.*'
      - 'packages/**'
      - 'plan9/**'
      - 'projects/**'
      - 'winbuild/**'

permissions: {}

env:
  MAKEFLAGS: -j 5
  CURL_CI: github
  DEBIAN_FRONTEND: noninteractive

jobs:
  cmake:
    name: linux (cmake & autoconf)
    runs-on: 'ubuntu-latest'
    container: 'debian:stretch'

    steps:
      - name: 'install prereqs'
        # Remember, this shell is dash, not bash
        run: |
          sed -E -i -e s@[a-z]+\.debian\.org/@archive.debian.org/debian-archive/@ -e '/ stretch-updates /d' /etc/apt/sources.list
          apt-get -o Dpkg::Use-Pty=0 update
          # See comment above if this fails after 2025-05-20
          apt-get -o Dpkg::Use-Pty=0 install -y --no-install-suggests --no-install-recommends httrack
          httrack --get https://deb.freexian.com/extended-lts/pool/main/f/freexian-archive-keyring/freexian-archive-keyring_2022.06.08_all.deb
          dpkg -i freexian-archive-keyring_2022.06.08_all.deb
          echo 'deb http://deb.freexian.com/extended-lts stretch-lts main contrib non-free' | tee /etc/apt/sources.list.d/extended-lts.list
          apt-get -o Dpkg::Use-Pty=0 update
          apt-get -o Dpkg::Use-Pty=0 install -y --no-install-suggests --no-install-recommends cmake make automake autoconf libtool gcc pkg-config libpsl-dev libzstd-dev zlib1g-dev libssl1.0-dev libssh-dev libssh2-1-dev libc-ares-dev heimdal-dev libldap2-dev librtmp-dev stunnel4 groff
          # GitHub's actions/checkout needs a newer glibc. This one is the
          # latest available for buster, the next stable release after stretch.
          httrack --get https://security.debian.org/debian-security/pool/updates/main/g/glibc/libc6_2.28-10+deb10u4_amd64.deb
          dpkg -i libc6_*_amd64.deb

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: 'cmake build-only (out-of-tree, libssh2)'
        run: |
          mkdir bld-1
          cd bld-1
          cmake .. -DCMAKE_UNITY_BUILD=ON -DCURL_WERROR=ON -DBUILD_SHARED_LIBS=ON \
            -DENABLE_ARES=OFF -DCURL_ZSTD=OFF -DCURL_USE_GSSAPI=OFF -DCURL_USE_LIBSSH2=ON -DCURL_USE_LIBSSH=OFF -DUSE_LIBRTMP=ON
          make install
          src/curl --disable --version

      - name: 'cmake build-only curl_config.h'
        run: |
          echo '::group::raw'; cat bld-1/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld-1/lib/curl_config.h | sort || true

      # when this job can get a libssh version 0.9.0 or later, this should get
      # that enabled again
      - name: 'cmake generate (out-of-tree, c-ares, zstd, gssapi)'
        run: |
          mkdir bld-cares
          cd bld-cares
          cmake .. -DCMAKE_UNITY_BUILD=ON -DCURL_WERROR=ON -DBUILD_SHARED_LIBS=ON \
            -DENABLE_ARES=ON -DCURL_USE_GSSAPI=ON -DCURL_USE_LIBSSH2=OFF -DCURL_USE_LIBSSH=OFF -DUSE_LIBRTMP=ON \
            -DCURL_LIBCURL_VERSIONED_SYMBOLS=ON

      - name: 'cmake curl_config.h'
        run: |
          echo '::group::raw'; cat bld-cares/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld-cares/lib/curl_config.h | sort || true

      - name: 'cmake build'
        run: |
          make -C bld-cares
          bld-cares/src/curl --disable --version

      - name: 'cmake install'
        run: make -C bld-cares install

      - name: 'cmake build tests'
        run: make -C bld-cares testdeps

      - name: 'cmake run tests'
        run: make -C bld-cares test-ci

      - name: 'autoreconf'
        run: autoreconf -if

      - name: 'configure (out-of-tree, c-ares, libssh2, zstd, gssapi)'
        run: |
          mkdir bld-am
          cd bld-am
          ../configure --disable-dependency-tracking --enable-unity --enable-warnings --enable-werror \
            --with-openssl --enable-ares --with-libssh2 --with-zstd --with-gssapi --with-librtmp \
            --prefix="$PWD"/../curl-install-am

      - name: 'autoconf curl_config.h'
        run: |
          echo '::group::raw'; cat bld-am/lib/curl_config.h || true; echo '::endgroup::'
          grep -F '#define' bld-am/lib/curl_config.h | sort || true

      - name: 'autoconf build'
        run: |
          make -C bld-am
          bld-am/src/curl --disable --version

      - name: 'autoconf install'
        run: make -C bld-am install

      - name: 'autoconf build tests'
        run: make -C bld-am/tests all
