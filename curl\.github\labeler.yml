# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# SPDX-License-Identifier: curl

# The workflow configures the .github/workflows/label.yml action
# to add labels to pull requests. This is not (yet?) a replacement for human
# triaging, but is intended to add labels to the easy cases. If the matching
# language becomes more powerful, more cases should be able to be handled.
#
# Labels are added in two ways: the any-glob-to-all-files ones are added if all
# the files fit into the category, and the any-glob-to-any-file ones are added
# as long as any file matches. The first ones are for "major" categories (the
# PR is all about that one topic, like HTTP/3), while the second ones are
# "addendums" that give useful information about a PR that's really mostly
# something else (e.g. CI if the PR also touches CI jobs).
#
# N.B. any-glob-to-all-files is misnamed; it acts like one-glob-to-all-files.
# Therefore, to get any-glob-to-all-files semantics with multiple matching
# patterns, they must be joined with commas to a single string surrounded by
# braces. For example: '{lib/**,src/**}'.
#
# See https://github.com/actions/labeler/ for documentation on this file.
---

appleOS:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              .github/workflows/macos.yml,\
              lib/config-mac.h,\
              lib/macos*\
              }"

authentication:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/mk-ca-bundle.1,\
              docs/libcurl/opts/CURLINFO_HTTPAUTH*,\
              docs/libcurl/opts/CURLINFO_PROXYAUTH*,\
              docs/libcurl/opts/CURLOPT_KRB*,\
              docs/libcurl/opts/CURLOPT_SASL*,\
              docs/libcurl/opts/CURLOPT_SERVICE_NAME*,\
              docs/libcurl/opts/CURLOPT_USERNAME*,\
              docs/libcurl/opts/CURLOPT_USERPWD*,\
              docs/libcurl/opts/CURLOPT_XOAUTH*,\
              lib/*gssapi*,\
              lib/*krb5*,\
              lib/*ntlm*,\
              lib/curl_sasl.*,\
              lib/http_aws*,\
              lib/http_digest.*,\
              lib/http_negotiate.*,\
              lib/vauth/**\
              }"

build:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              **/CMakeLists.txt,\
              **/Makefile.am,\
              **/Makefile.inc,\
              **/*.m4,\
              **/*.mk,\
              *.m4,\
              docs/INSTALL-CMAKE.md,\
              lib/curl_config.h.cmake,\
              lib/libcurl*.in,\
              CMake/**,\
              CMakeLists.txt,\
              configure.ac,\
              m4/**,\
              Makefile.*,\
              packages/**,\
              plan9/**,\
              projects/**,\
              winbuild/**,\
              lib/libcurl.def,\
              tests/cmake/**\
              }"

CI:
  - all:
      - changed-files:
          - any-glob-to-any-file:
              - '.circleci/**'
              - '.github/**'
              - 'appveyor.*'
              - 'scripts/ci*'
              - 'tests/azure.pm'
              - 'tests/appveyor.pm'
              - 'tests/CI.md'

cmake:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              **/CMakeLists.txt,\
              CMake/**,\
              docs/INSTALL-CMAKE.md,\
              lib/curl_config.h.cmake,\
              tests/cmake/**\
              }"

cmdline tool:
  - all:
      - changed-files:
          - any-glob-to-any-file:
              - 'docs/cmdline-opts/**'
              - 'src/**'

connecting & proxies:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/internals/CONNECTION-FILTERS.md,\
              docs/examples/ipv6.c,\
              docs/libcurl/opts/CURLINFO_CONNECT*,\
              docs/libcurl/opts/CURLINFO_PROXY*,\
              docs/libcurl/opts/CURLOPT_ADDRESS*,\
              docs/libcurl/opts/CURLOPT_CONNECT*,\
              docs/libcurl/opts/CURLOPT_HAPROXY*,\
              docs/libcurl/opts/CURLOPT_OPENSOCKET*,\
              docs/libcurl/opts/CURLOPT_PRE_PROXY*,\
              docs/libcurl/opts/CURLOPT_PROXY*,\
              docs/libcurl/opts/CURLOPT_SOCKOPT*,\
              docs/libcurl/opts/CURLOPT_SOCKS*,\
              docs/libcurl/opts/CURLOPT_TCP*,\
              docs/libcurl/opts/CURLOPT_TIMEOUT*,\
              lib/cf-*proxy.*,\
              lib/cf-socket.*,\
              lib/cfilters.*,\
              lib/conncache.*,\
              lib/connect.*,\
              lib/http_proxy.*,\
              lib/if2ip.*,\
              lib/noproxy.*,\
              lib/socks.*,\
              tests/server/socksd.c\
              }"

cookies:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/HTTP-COOKIES.md,\
              docs/cmdline-opts/cookie*,\
              docs/cmdline-opts/junk-session-cookies.md,\
              docs/libcurl/opts/CURLINFO_COOKIE*,\
              docs/libcurl/opts/CURLOPT_COOKIE*,\
              docs/examples/cookie_interface.c,\
              lib/cookie.*,\
              lib/psl.*\
              }"

cryptography:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/CIPHERS.md,\
              docs/RUSTLS.md,\
              docs/libcurl/opts/CURLOPT_EGDSOCKET*,\
              lib/*sha256*,\
              lib/*sha512*,\
              lib/curl_des.*,\
              lib/curl_hmac.*,\
              lib/curl_md?.*,\
              lib/md?.*,\
              lib/rand.*\
              }"

DICT:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              lib/dict.*,\
              tests/dictserver.py\
              }"

documentation:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              .github/workflows/checkdocs.yml,\
              .github/scripts/badwords.*,\
              .github/scripts/cd2cd,\
              .github/scripts/cd2nroff,\
              .github/scripts/cdall.pl,\
              .github/scripts/nroff2cd,\
              .github/scripts/verify-examples.pl,\
              .github/scripts/verify-synopsis.pl,\
              **/*.md,\
              **/*.txt,\
              **/*.1,\
              CHANGES.md,\
              docs/**,\
              LICENSES/**,\
              README,\
              RELEASE-NOTES,\
              scripts/cd*\
              }"
          - all-globs-to-all-files:
              # negative matches
              - '!**/CMakeLists.txt'
              - '!**/Makefile.am'

FTP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/libcurl/opts/CURLINFO_FTP*,\
              docs/libcurl/opts/CURLOPT_FTP*,\
              docs/libcurl/opts/CURLOPT_WILDCARDMATCH*,\
              docs/examples/ftp*,\
              lib/curl_fnmatch.*,\
              lib/curl_range.*,\
              lib/ftp*,\
              tests/ftp*\
              }"

GOPHER:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              lib/gopher*\
              }"

HTTP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/examples/hsts*,\
              docs/examples/http-*,\
              docs/examples/httpput*,\
              docs/examples/https*,\
              docs/examples/*post*,\
              docs/HTTP-COOKIES.md,\
              docs/libcurl/opts/CURLINFO_COOKIE*,\
              docs/libcurl/opts/CURLOPT_COOKIE*,\
              docs/libcurl/opts/CURLINFO_HTTP_**,\
              docs/libcurl/opts/CURLINFO_REDIRECT*,\
              docs/libcurl/opts/CURLINFO_REFER*,\
              docs/libcurl/opts/CURLOPT_FOLLOWLOCATION*,\
              docs/libcurl/opts/CURLOPT_HSTS*,\
              docs/libcurl/opts/CURLOPT_HTTP*,\
              docs/libcurl/opts/CURLOPT_POST.*,\
              docs/libcurl/opts/CURLOPT_POSTFIELD*,\
              docs/libcurl/opts/CURLOPT_POSTREDIR*,\
              docs/libcurl/opts/CURLOPT_REDIR*,\
              docs/libcurl/opts/CURLOPT_REFER*,\
              docs/libcurl/opts/CURLOPT_TRAILER*,\
              docs/libcurl/opts/CURLOPT_TRANSFER_ENCODING*,\
              lib/cf-https*,\
              lib/cf-h1*,\
              lib/cf-h2*,\
              lib/cookie.*,\
              lib/hsts.*,\
              lib/http*,\
              tests/http*,\
              tests/http-server.pl,\
              tests/http/*,\
              tests/nghttp*\
              }"

HTTP/2:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              CMake/FindNGHTTP2.cmake,\
              CMake/FindQuiche.cmake,\
              docs/libcurl/opts/CURLOPT_STREAM*,\
              docs/examples/http2*,\
              lib/http2*,\
              tests/http2-server.pl\
              }"

HTTP/3:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              .github/workflows/ngtcp2*,\
              .github/workflows/quiche*,\
              .github/workflows/osslq*,\
              CMake/FindMSH3.cmake,\
              CMake/FindNGHTTP3.cmake,\
              CMake/FindNGTCP2.cmake,\
              docs/HTTP3.md,\
              docs/examples/http3*,\
              lib/vquic/**,\
              tests/http3-server.pl,\
              tests/nghttpx.conf\
              }"

IMAP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              lib/imap*,\
              docs/examples/imap*\
              }"

LDAP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              lib/*ldap*\
              }"

libcurl API:
  - all:
      - changed-files:
          - any-glob-to-any-file:
              - 'docs/libcurl/ABI.md'
              - 'docs/libcurl/curl_*.md'
              - 'include/curl/**'

logging:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/cmdline-opts/trace*,\
              docs/libcurl/curl_global_trace*,\
              lib/curl_trc*,\
              tests/http/test_15_tracing.py\
              }"

MIME:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/libcurl/curl_form*,\
              docs/libcurl/curl_mime_*,\
              docs/libcurl/opts/CURLOPT_MIME*,\
              docs/libcurl/opts/CURLOPT_HTTPPOST*,\
              lib/formdata*,\
              lib/mime*,\
              src/tool_formparse.*\
              }"

MQTT:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              lib/mqtt*,\
              tests/server/mqttd.c\
              }"

name lookup:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/examples/resolve.c,\
              docs/libcurl/opts/CURLINFO_NAMELOOKUP*,\
              docs/libcurl/opts/CURLOPT_DNS*,\
              docs/libcurl/opts/CURLOPT_DOH*,\
              docs/libcurl/opts/CURLOPT_RESOLVE*,\
              lib/asyn*,\
              lib/curl_gethostname.*,\
              lib/doh*,\
              lib/host*,\
              lib/idn*,\
              lib/inet_pton.*,\
              lib/socketpair*,\
              tests/server/resolve.c\
              }"

POP3:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/examples/pop3*,\
              lib/pop3.*\
              }"

RTMP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              CMake/FindLibrtmp.cmake,\
              lib/curl_rtmp.*\
              }"

RTSP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/libcurl/opts/CURLINFO_RTSP*,\
              docs/libcurl/opts/CURLOPT_RTSP*,\
              lib/rtsp.*,\
              tests/rtspserver.pl,\
              tests/server/rtspd.c\
              }"

SCP/SFTP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              CMake/FindLibssh2.cmake,\
              docs/libcurl/opts/CURLOPT_SSH*,\
              docs/examples/sftp*,\
              lib/vssh/**,\
              tests/sshhelp.pm,\
              tests/sshserver.pl\
              }"

script:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              **/*.pl,\
              **/*.sh,\
              curl-config.in,\
              docs/curl-config.1,\
              docs/mk-ca-bundle.1,\
              docs/THANKS-filter,\
              scripts/**\
              }"

SMB:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              lib/smb.*,\
              tests/smbserver.py\
              }"

SMTP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/examples/smtp-*,\
              docs/libcurl/opts/CURLOPT_MAIL*,\
              lib/smtp.*\
              }"

tests:
  - all:
      - changed-files:
          - any-glob-to-any-file:
              - 'tests/**'

TFTP:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              lib/tftp.*,\
              tests/tftpserver.pl,\
              tests/server/tftp*\
              }"

TLS:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              CMake/FindMbedTLS.cmake,\
              CMake/FindWolfSSL.cmake,\
              CMake/FindRustls.cmake,\
              docs/examples/ssl*,\
              docs/examples/*ssl.*,\
              docs/examples/*tls.*,\
              docs/SSL*,\
              docs/libcurl/curl_global_sslset*,\
              docs/libcurl/opts/CURLINFO_CA*,\
              docs/libcurl/opts/CURLINFO_CERT*,\
              docs/libcurl/opts/CURLINFO_SSL*,\
              docs/libcurl/opts/CURLINFO_TLS*,\
              docs/libcurl/opts/CURLOPT_CA*,\
              docs/libcurl/opts/CURLOPT_CERT*,\
              docs/libcurl/opts/CURLOPT_PINNEDPUBLICKEY*,\
              docs/libcurl/opts/CURLOPT_SSL*,\
              docs/libcurl/opts/CURLOPT_TLS*,\
              docs/libcurl/opts/CURLOPT_USE_SSL*,\
              lib/vtls/**,\
              m4/curl-gnutls.m4,\
              m4/curl-mbedtls.m4,\
              m4/curl-openssl.m4,\
              m4/curl-rustls.m4,\
              m4/curl-schannel.m4,\
              m4/curl-wolfssl.m4\
              }"

URL:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/libcurl/curl_url*,\
              docs/URL-SYNTAX.md,\
              docs/examples/parseurl*,\
              include/curl/urlapi.h,\
              lib/urlapi*\
              }"

WebSocket:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              docs/internals/WEBSOCKET.md*,\
              docs/examples/websocket*,\
              docs/libcurl/curl_ws_*,\
              docs/libcurl/libcurl-ws*,\
              docs/libcurl/opts/CURLOPT_WS_*,\
              include/curl/websockets.h,\
              lib/ws.*,\
              tests/http/clients/ws*,\
              tests/http/test_20_websockets.py,\
              tests/http/testenv/ws*\
              }"

Windows:
  - all:
      - changed-files:
          - any-glob-to-all-files: "{\
              appveyor.*,\
              .github/workflows/windows.yml,\
              CMake/win32-cache.cmake,\
              lib/*win32*,\
              lib/curlx/multibyte.*,\
              lib/rename.*,\
              lib/vtls/schannel*,\
              m4/curl-schannel.m4,\
              projects/**,\
              src/tool_doswin.c,\
              winbuild/**,\
              lib/libcurl.def\
              }"
